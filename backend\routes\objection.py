from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import mysql.connector
from models.db import get_db
from ai_modules.prompts import build_objection_prompt
from ai_modules.generation import generate_text

router = APIRouter()

class ObjectionRequest(BaseModel):
    application_number: str
    model_choice: str # 'gemini' or 'ollama'

@router.post('/generate-objection-responses')
def generate_objection_responses(request: ObjectionRequest):
    conn = None
    try:
        conn = get_db()
        cursor = conn.cursor(buffered=True, dictionary=True)
        # Fetch FER, spec, claims text
        cursor.execute("SELECT extracted_text FROM documents WHERE application_number=%s AND type='FER'", (request.application_number,))
        fer = cursor.fetchone()
        cursor.execute("SELECT extracted_text FROM documents WHERE application_number=%s AND type='Specification'", (request.application_number,))
        spec = cursor.fetchone()
        cursor.execute("SELECT extracted_text FROM documents WHERE application_number=%s AND type='Claims'", (request.application_number,))
        claims = cursor.fetchone()
        fer_text = fer['extracted_text'] if fer else ''
        spec_text = spec['extracted_text'] if spec else ''
        claims_text = claims['extracted_text'] if claims else ''
        if not fer_text:
            raise HTTPException(status_code=404, detail='FER not found for this application')
        prompt = build_objection_prompt(fer_text, spec_text, claims_text)

        # Generate responses using the new centralized function
        responses = generate_text(request.model_choice, prompt)

        # Store in application_examination_details
        cursor.execute("""
            UPDATE application_examination_details
            SET objection_responses = %s, objections_generated_by = %s
            WHERE application_number = %s
        """, (responses, request.model_choice, request.application_number))
        conn.commit()

        return {"responses": responses, "generated_by": request.model_choice}
    except Exception as e:
        print(f"Error during objection response generation: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close() 