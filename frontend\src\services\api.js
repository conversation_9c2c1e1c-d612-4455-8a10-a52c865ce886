/* All Axios/Fetch API calls will be defined here */

import axios from "axios";

const API_URL = "http://*************:8000/api";

const apiClient = axios.create({
  baseURL: API_URL,
});

export const uploadDocuments = async (formData) => {
  const response = await apiClient.post("/upload", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

export const generateSummary = async (patent_id, model) => {
  const response = await apiClient.post("/generate-summary", {
    patent_id: parseInt(patent_id),
    model_choice: model,
  });
  return response.data;
};

export const generateObjectionResponses = async (patent_id, model) => {
  const response = await apiClient.post("/generate-objection-responses", {
    patent_id: parseInt(patent_id),
    model_choice: model,
  });
  return response.data;
};

export const listPatents = async () => {
  const response = await apiClient.get("/patents");
  return response.data;
};

export const getPatentDetails = async (patent_id) => {
  const response = await apiClient.get(`/patents/${patent_id}`);
  return response.data;
};

export const saveSummary = async (patent_id, summary) => {
  const response = await apiClient.put(`/patents/${patent_id}/summary`, {
    summary,
  });
  return response.data;
};

export const saveObjections = async (patent_id, objections) => {
  const response = await apiClient.put(`/patents/${patent_id}/objections`, {
    objections,
  });
  return response.data;
};

export const deletePatent = async (patent_id) => {
  const response = await apiClient.delete(`/patents/${patent_id}`);
  return response.data;
};

export const getExaminationDetails = async (applicationNumber) => {
  const response = await apiClient.get(
    `/examination-details/${applicationNumber}`
  );
  return response.data;
};

export const updateExaminationDetails = async (applicationNumber, details) => {
  const response = await apiClient.put(
    `/examination-details/${applicationNumber}`,
    details
  );
  return response.data;
};

export const listExaminationDetails = async () => {
  const response = await apiClient.get("/examination-details");
  return response.data;
};

export const getFerDocumentPath = async (applicationNumber) => {
  const response = await apiClient.get(`/documents/fer/${applicationNumber}`);
  return response.data;
};

// Placeholder for other API calls
// export const getSummary = async (applicationNumber) => { ... }
// export const getObjectionReplies = async (applicationNumber) => { ... }
