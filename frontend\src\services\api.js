/* All Axios/Fetch API calls will be defined here */

import axios from "axios";

const API_URL = "http://*************:8000/api";

const apiClient = axios.create({
  baseURL: API_URL,
});

export const uploadDocuments = async (formData) => {
  const response = await apiClient.post("/upload", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

export const generateSummary = async (application_number, model) => {
  const response = await apiClient.post("/generate-summary", {
    application_number: application_number,
    model_choice: model,
  });
  return response.data;
};

export const generateObjectionResponses = async (application_number, model) => {
  const response = await apiClient.post("/generate-objection-responses", {
    application_number: application_number,
    model_choice: model,
  });
  return response.data;
};

export const listApplications = async () => {
  const response = await apiClient.get("/applications");
  return response.data;
};

export const getApplicationDetails = async (application_number) => {
  const response = await apiClient.get(`/applications/${application_number}`);
  return response.data;
};

export const saveSummary = async (application_number, summary) => {
  const response = await apiClient.put(`/applications/${application_number}/summary`, {
    summary,
  });
  return response.data;
};

export const saveObjections = async (application_number, objections) => {
  const response = await apiClient.put(`/applications/${application_number}/objections`, {
    objections,
  });
  return response.data;
};

export const deleteApplication = async (application_number) => {
  const response = await apiClient.delete(`/applications/${application_number}`);
  return response.data;
};

export const getExaminationDetails = async (applicationNumber) => {
  const response = await apiClient.get(
    `/examination-details/${applicationNumber}`
  );
  return response.data;
};

export const updateExaminationDetails = async (applicationNumber, details) => {
  const response = await apiClient.put(
    `/examination-details/${applicationNumber}`,
    details
  );
  return response.data;
};

export const listExaminationDetails = async () => {
  const response = await apiClient.get("/examination-details");
  return response.data;
};

export const getFerDocumentPath = async (applicationNumber) => {
  const response = await apiClient.get(`/documents/fer/${applicationNumber}`);
  return response.data;
};

// FER Summary API calls
export const getFerSummary = async (applicationNumber) => {
  const response = await apiClient.get(`/fer-summary/${applicationNumber}`);
  return response.data;
};

export const createFerSummary = async (applicationNumber, summaryData) => {
  const response = await apiClient.post(`/fer-summary/${applicationNumber}`, summaryData);
  return response.data;
};

export const updateFerSummary = async (applicationNumber, summaryData) => {
  const response = await apiClient.put(`/fer-summary/${applicationNumber}`, summaryData);
  return response.data;
};

// Placeholder for other API calls
// export const getSummary = async (applicationNumber) => { ... }
// export const getObjectionReplies = async (applicationNumber) => { ... }
