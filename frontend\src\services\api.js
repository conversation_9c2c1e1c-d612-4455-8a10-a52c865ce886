/* All Axios/Fetch API calls will be defined here */

import axios from "axios";

const API_URL = "http://localhost:8000/api";

const apiClient = axios.create({
  baseURL: API_URL,
});

export const uploadDocuments = async (formData) => {
  const response = await apiClient.post("/upload", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};



export const listApplications = async () => {
  const response = await apiClient.get("/applications");
  return response.data;
};

export const getApplicationDetails = async (application_number) => {
  const response = await apiClient.get(`/applications/${application_number}`);
  return response.data;
};



export const deleteApplication = async (application_number) => {
  const response = await apiClient.delete(`/applications/${application_number}`);
  return response.data;
};

export const getExaminationDetails = async (applicationNumber) => {
  const response = await apiClient.get(
    `/examination-details/${applicationNumber}`
  );
  return response.data;
};

export const updateExaminationDetails = async (applicationNumber, details) => {
  const response = await apiClient.put(
    `/examination-details/${applicationNumber}`,
    details
  );
  return response.data;
};

export const listExaminationDetails = async () => {
  const response = await apiClient.get("/examination-details");
  return response.data;
};

export const getFerDocumentPath = async (applicationNumber) => {
  const response = await apiClient.get(`/documents/fer/${applicationNumber}`);
  return response.data;
};

// FER Summary API calls
export const getFerSummary = async (applicationNumber) => {
  const response = await apiClient.get(`/fer-summary/${applicationNumber}`);
  return response.data;
};

export const createFerSummary = async (applicationNumber, summaryData) => {
  const response = await apiClient.post(`/fer-summary/${applicationNumber}`, summaryData);
  return response.data;
};

export const updateFerSummary = async (applicationNumber, summaryData) => {
  const response = await apiClient.put(`/fer-summary/${applicationNumber}`, summaryData);
  return response.data;
};

// Cited Documents API calls
export const getCitedDocuments = async (applicationNumber) => {
  const response = await apiClient.get(`/cited-documents/${applicationNumber}`);
  return response.data;
};

export const updateCitedDocuments = async (applicationNumber, documentsData) => {
  const response = await apiClient.put(`/cited-documents/${applicationNumber}`, documentsData);
  return response.data;
};

// Placeholder for other API calls
// export const getSummary = async (applicationNumber) => { ... }
// export const getObjectionReplies = async (applicationNumber) => { ... }
