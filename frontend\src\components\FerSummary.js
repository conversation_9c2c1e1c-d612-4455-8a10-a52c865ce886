import React, { useState, useEffect } from "react";
import { getFerSummary, updateFerSummary } from "../services/api";
import toast from "react-hot-toast";

export default function FerSummary({ applicationNumber }) {
  const [summaryData, setSummaryData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({});
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    const fetchSummary = async () => {
      if (!applicationNumber) return;
      
      setIsLoading(true);
      try {
        const response = await getFerSummary(applicationNumber);
        setSummaryData(response.summary);
        if (response.summary) {
          setEditData(response.summary);
        }
      } catch (error) {
        console.error("Failed to fetch FER summary:", error);
        toast.error("Failed to load FER summary data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSummary();
  }, [applicationNumber]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    if (summaryData) {
      setEditData(summaryData);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await updateFerSummary(applicationNumber, editData);
      setSummaryData(editData);
      setIsEditing(false);
      toast.success("FER summary updated successfully");
    } catch (error) {
      console.error("Failed to update FER summary:", error);
      toast.error("Failed to update FER summary");
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field, value) => {
    setEditData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Helper function to combine multi-row data into single display row
  const getCombinedClaimsValue = (claims1Field, claims2Field) => {
    const claims1 = summaryData?.[claims1Field] || "";
    const claims2 = summaryData?.[claims2Field] || "";

    // Combine non-empty values
    const values = [claims1, claims2].filter(val => val && val !== "NONE" && val.trim() !== "");
    return values.length > 0 ? values.join(", ") : "";
  };

  const getCombinedRemarkValue = (remark1Field, remark2Field) => {
    const remark1 = summaryData?.[remark1Field] || "";
    const remark2 = summaryData?.[remark2Field] || "";

    // Priority: Yes > No > empty, and show additional info if present
    if (remark1 && remark1.toLowerCase().includes('yes')) return remark1;
    if (remark2 && remark2.toLowerCase().includes('yes')) return remark2;
    if (remark1 && remark1.toLowerCase().includes('no')) return remark1;
    if (remark2 && remark2.toLowerCase().includes('no')) return remark2;
    return remark1 || remark2 || "";
  };

  const renderSingleRowField = (label, claims1Field, remark1Field, claims2Field, remark2Field) => {
    // For display: combine multi-row data into single row
    const displayClaimsValue = isEditing ?
      (editData[claims1Field] || "") :
      getCombinedClaimsValue(claims1Field, claims2Field);

    const displayRemarkValue = isEditing ?
      (editData[remark1Field] || "") :
      getCombinedRemarkValue(remark1Field, remark2Field);

    return (
      <tr className="border-b border-gray-200">
        <td className="px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50">
          {label}
        </td>
        <td className="px-4 py-3">
          {isEditing ? (
            <input
              type="text"
              value={displayClaimsValue}
              onChange={(e) => handleInputChange(claims1Field, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Enter claim numbers"
            />
          ) : (
            <span className="text-sm text-gray-900">{displayClaimsValue || "NONE"}</span>
          )}
        </td>
        <td className="px-4 py-3">
          {isEditing ? (
            <input
              type="text"
              value={displayRemarkValue}
              onChange={(e) => handleInputChange(remark1Field, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Yes/No"
            />
          ) : (
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              displayRemarkValue?.toLowerCase().includes('yes') ? 'bg-green-100 text-green-800' :
              displayRemarkValue?.toLowerCase().includes('no') ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {displayRemarkValue || ""}
            </span>
          )}
        </td>
      </tr>
    );
  };

  const renderRemarkOnlyField = (label, remarkField) => {
    const remarkValue = isEditing ? (editData[remarkField] || "") : (summaryData?.[remarkField] || "");

    return (
      <tr className="border-b border-gray-200">
        <td className="px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50">
          {label}
        </td>
        <td className="px-4 py-3 text-sm text-gray-500">
          --
        </td>
        <td className="px-4 py-3">
          {isEditing ? (
            <input
              type="text"
              value={remarkValue}
              onChange={(e) => handleInputChange(remarkField, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Yes/No"
            />
          ) : (
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              remarkValue?.toLowerCase().includes('yes') ? 'bg-green-100 text-green-800' :
              remarkValue?.toLowerCase().includes('no') ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {remarkValue || ""}
            </span>
          )}
        </td>
      </tr>
    );
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800">FER Summary (Part-I)</h2>
        </div>
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-2"></div>
          <span className="text-gray-500">Loading FER summary...</span>
        </div>
      </div>
    );
  }

  if (!summaryData) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800">FER Summary (Part-I)</h2>
        </div>
        <div className="p-8 text-center text-gray-500">
          <svg className="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p>No FER summary data available</p>
          <p className="text-sm mt-1">Summary data will be automatically extracted when you upload a FER document</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-xl font-bold text-gray-800">FER Summary (Part-I)</h2>
        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {isSaving ? "Saving..." : "Save"}
              </button>
            </>
          ) : (
            <button
              onClick={handleEdit}
              className="px-4 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded-md hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              Edit
            </button>
          )}
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Requirements under the Act
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Claim Numbers
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Remarks
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {renderSingleRowField("Novelty", "novelty_claims1", "novelty_remark1", "novelty_claims2", "novelty_remark2")}
            {renderSingleRowField("Inventive Step", "inventive_step_claims1", "inventive_step_remark1", "inventive_step_claims2", "inventive_step_remark2")}
            {renderSingleRowField("Industrial Applicability", "industrial_applicability_claims1", "industrial_applicability_remark1", "industrial_applicability_claims2", "industrial_applicability_remark2")}
            {renderSingleRowField("Nonpatentability u/s 3", "section_3_claims1", "section_3_remark1", "section_3_claims2", "section_3_remark2")}
            {renderSingleRowField("Non-patentability u/s 4", "section_4_claims1", "section_4_remark1", "section_4_claims2", "section_4_remark2")}
            {renderSingleRowField("Unity of invention u/s 10(5)", "unity_of_invention_claims1", "unity_of_invention_remark1", "unity_of_invention_claims2", "unity_of_invention_remark2")}
            {renderRemarkOnlyField("Sufficiency of disclosure u/s 10(4)", "sufficiency_of_disclosure_remark1")}
            {renderRemarkOnlyField("Reference to co-pending/foreign applications", "reference_to_foreign_applications_remark1")}
            {renderSingleRowField("Clarity/Conciseness", "clarity_claims1", "clarity_remark1", "clarity_claims2", "clarity_remark2")}
            {renderSingleRowField("Definitive", "definitive_claims1", "definitive_remark1", "definitive_claims2", "definitive_remark2")}
            {renderSingleRowField("Supported by description", "supported_by_description_claims1", "supported_by_description_remark1", "supported_by_description_claims2", "supported_by_description_remark2")}
            {renderSingleRowField("Scope", "scope_claims1", "scope_remark1", "scope_claims2", "scope_remark2")}
          </tbody>
        </table>
      </div>
    </div>
  );
}
