import React, { useState, useEffect } from "react";
import { getFerSummary, updateFerSummary } from "../services/api";
import toast from "react-hot-toast";

export default function FerSummary({ applicationNumber }) {
  const [summaryData, setSummaryData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({});
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    const fetchSummary = async () => {
      if (!applicationNumber) return;
      
      setIsLoading(true);
      try {
        const response = await getFerSummary(applicationNumber);
        setSummaryData(response.summary);
        if (response.summary) {
          setEditData(response.summary);
        }
      } catch (error) {
        console.error("Failed to fetch FER summary:", error);
        toast.error("Failed to load FER summary data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSummary();
  }, [applicationNumber]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    if (summaryData) {
      setEditData(summaryData);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await updateFerSummary(applicationNumber, editData);
      setSummaryData(editData);
      setIsEditing(false);
      toast.success("FER summary updated successfully");
    } catch (error) {
      console.error("Failed to update FER summary:", error);
      toast.error("Failed to update FER summary");
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field, value) => {
    setEditData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const renderField = (label, claimsField, remarkField) => {
    const claimsValue = isEditing ? editData[claimsField] || "" : summaryData?.[claimsField] || "--";
    const remarkValue = isEditing ? editData[remarkField] || "" : summaryData?.[remarkField] || "--";

    return (
      <tr className="border-b border-gray-200">
        <td className="px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50">
          {label}
        </td>
        <td className="px-4 py-3">
          {isEditing ? (
            <input
              type="text"
              value={claimsValue}
              onChange={(e) => handleInputChange(claimsField, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="e.g., 1-28"
            />
          ) : (
            <span className="text-sm text-gray-900">{claimsValue}</span>
          )}
        </td>
        <td className="px-4 py-3">
          {isEditing ? (
            <select
              value={remarkValue}
              onChange={(e) => handleInputChange(remarkField, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="">Select...</option>
              <option value="Yes">Yes</option>
              <option value="No">No</option>
            </select>
          ) : (
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              remarkValue === 'Yes' ? 'bg-green-100 text-green-800' : 
              remarkValue === 'No' ? 'bg-red-100 text-red-800' : 
              'bg-gray-100 text-gray-800'
            }`}>
              {remarkValue}
            </span>
          )}
        </td>
      </tr>
    );
  };

  const renderSingleField = (label, field) => {
    const value = isEditing ? editData[field] || "" : summaryData?.[field] || "--";

    return (
      <tr className="border-b border-gray-200">
        <td className="px-4 py-3 text-sm font-medium text-gray-900 bg-gray-50">
          {label}
        </td>
        <td className="px-4 py-3" colSpan="2">
          {isEditing ? (
            <select
              value={value}
              onChange={(e) => handleInputChange(field, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="">Select...</option>
              <option value="Yes">Yes</option>
              <option value="No">No</option>
            </select>
          ) : (
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              value === 'Yes' ? 'bg-green-100 text-green-800' : 
              value === 'No' ? 'bg-red-100 text-red-800' : 
              'bg-gray-100 text-gray-800'
            }`}>
              {value}
            </span>
          )}
        </td>
      </tr>
    );
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800">FER Summary (Part-I)</h2>
        </div>
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-2"></div>
          <span className="text-gray-500">Loading FER summary...</span>
        </div>
      </div>
    );
  }

  if (!summaryData) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800">FER Summary (Part-I)</h2>
        </div>
        <div className="p-8 text-center text-gray-500">
          <svg className="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p>No FER summary data available</p>
          <p className="text-sm mt-1">Summary data will be automatically extracted when you upload a FER document</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-xl font-bold text-gray-800">FER Summary (Part-I)</h2>
        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {isSaving ? "Saving..." : "Save"}
              </button>
            </>
          ) : (
            <button
              onClick={handleEdit}
              className="px-4 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded-md hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              Edit
            </button>
          )}
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Requirements under the Act
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Claim Numbers
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Remarks
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {renderField("Novelty", "novelty_claims", "novelty_remark")}
            {renderField("Inventive Step", "inventive_step_claims", "inventive_step_remark")}
            {renderField("Industrial Applicability", "industrial_applicability_claims", "industrial_applicability_remark")}
            {renderField("Nonpatentability u/s 3", "section_3_claims", "section_3_remark")}
            {renderField("Non-patentability u/s 4", "section_4_claims", "section_4_remark")}
            {renderField("Unity of invention u/s 10(5)", "unity_of_invention_claims", "unity_of_invention_remark")}
            {renderSingleField("Sufficiency of disclosure u/s 10(4)", "sufficiency_of_disclosure")}
            {renderSingleField("Reference to co-pending/foreign applications", "reference_to_foreign_applications")}
            {renderField("Clarity/Conciseness", "clarity_claims", "clarity_remark")}
            {renderField("Definitive", "definitive_claims", "definitive_remark")}
            {renderField("Supported by description", "supported_by_description_claims", "supported_by_description_remark")}
            {renderField("Scope", "scope_claims", "scope_remark")}
          </tbody>
        </table>
      </div>
    </div>
  );
}
