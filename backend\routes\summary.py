from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import mysql.connector
from models.db import get_db
from ai_modules.prompts import build_summary_prompt
from ai_modules.generation import generate_text

router = APIRouter()

class SummaryRequest(BaseModel):
    patent_id: int
    model_choice: str  # 'gemini' or 'ollama'

@router.post('/generate-summary')
def generate_summary(request: SummaryRequest):
    conn = None
    try:
        conn = get_db()
        cursor = conn.cursor(buffered=True, dictionary=True)
        # Fetch documents from DB
        cursor.execute("SELECT extracted_text FROM documents WHERE patent_id=%s AND type='FER'", (request.patent_id,))
        fer = cursor.fetchone()
        cursor.execute("SELECT extracted_text FROM documents WHERE patent_id=%s AND type='Specification'", (request.patent_id,))
        spec = cursor.fetchone()
        cursor.execute("SELECT extracted_text FROM documents WHERE patent_id=%s AND type='Claims'", (request.patent_id,))
        claims = cursor.fetchone()
    
        fer_text = fer['extracted_text'] if fer else ''
        spec_text = spec['extracted_text'] if spec else ''
        claims_text = claims['extracted_text'] if claims else ''

        if not fer_text:
            raise HTTPException(status_code=404, detail='FER not found for this patent')

        # Build the prompt
        prompt = build_summary_prompt(fer_text, spec_text, claims_text)

        # Generate the summary using the new centralized function
        summary = generate_text(request.model_choice, prompt)

        # Store in fer_replies
        cursor.execute("INSERT INTO fer_replies (patent_id, section, content, generated_by) VALUES (%s, %s, %s, %s)",
                       (request.patent_id, 'Summary', summary, request.model_choice))
        conn.commit()
        
        return {"summary": summary, "generated_by": request.model_choice}
    except Exception as e:
        print(f"Error during summary generation: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close() 