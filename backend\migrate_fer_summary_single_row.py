#!/usr/bin/env python3
"""
Migration script to convert FER summary table from multi-row to single-row structure
"""

import mysql.connector
from models.db import get_db
import json

def backup_existing_data():
    """Backup existing FER summary data"""
    conn = get_db()
    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("SELECT * FROM application_fer_summary")
            data = cursor.fetchall()
            
            # Save backup to file
            with open('fer_summary_backup.json', 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            print(f"✅ Backed up {len(data)} records to fer_summary_backup.json")
            return data
    finally:
        conn.close()

def create_new_table():
    """Create new single-row FER summary table"""
    conn = get_db()
    try:
        with conn.cursor() as cursor:
            # Drop existing table
            cursor.execute("DROP TABLE IF EXISTS application_fer_summary")
            
            # Create new table with single-row structure
            cursor.execute("""
                CREATE TABLE application_fer_summary (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    application_number VARCHAR(50) NOT NULL,

                    -- Novelty
                    novelty_claims VARCHAR(100),
                    novelty_remark VARCHAR(100),

                    -- Inventive Step
                    inventive_step_claims VARCHAR(100),
                    inventive_step_remark VARCHAR(100),

                    -- Industrial Applicability
                    industrial_applicability_claims VARCHAR(100),
                    industrial_applicability_remark VARCHAR(100),

                    -- Section 3 (Non-patentability)
                    section_3_claims VARCHAR(100),
                    section_3_remark VARCHAR(100),

                    -- Section 4 (Non-patentability)
                    section_4_claims VARCHAR(100),
                    section_4_remark VARCHAR(100),

                    -- Unity of Invention
                    unity_of_invention_claims VARCHAR(100),
                    unity_of_invention_remark VARCHAR(100),

                    -- Sufficiency of Disclosure (only remark, no claims)
                    sufficiency_of_disclosure VARCHAR(100),

                    -- Reference to Foreign Applications (only remark, no claims)
                    reference_to_foreign_applications VARCHAR(100),

                    -- Clarity
                    clarity_claims VARCHAR(100),
                    clarity_remark VARCHAR(100),

                    -- Definitive
                    definitive_claims VARCHAR(100),
                    definitive_remark VARCHAR(100),

                    -- Supported by Description
                    supported_by_description_claims VARCHAR(100),
                    supported_by_description_remark VARCHAR(100),

                    -- Scope
                    scope_claims VARCHAR(100),
                    scope_remark VARCHAR(100),

                    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                    FOREIGN KEY (application_number)
                        REFERENCES application_examination_details(application_number)
                        ON DELETE CASCADE
                )
            """)
            
            conn.commit()
            print("✅ Created new single-row FER summary table")
    finally:
        conn.close()

def migrate_data(backup_data):
    """Migrate data from multi-row to single-row structure"""
    conn = get_db()
    try:
        with conn.cursor() as cursor:
            for record in backup_data:
                # Map multi-row data to single-row structure
                # Take the first row of data for each requirement
                # Convert empty strings and "NONE" to None
                def clean_value(val):
                    if val in ('', 'NONE', None):
                        return None
                    return val

                cursor.execute("""
                    INSERT INTO application_fer_summary (
                        application_number,
                        novelty_claims, novelty_remark,
                        inventive_step_claims, inventive_step_remark,
                        industrial_applicability_claims, industrial_applicability_remark,
                        section_3_claims, section_3_remark,
                        section_4_claims, section_4_remark,
                        unity_of_invention_claims, unity_of_invention_remark,
                        sufficiency_of_disclosure,
                        reference_to_foreign_applications,
                        clarity_claims, clarity_remark,
                        definitive_claims, definitive_remark,
                        supported_by_description_claims, supported_by_description_remark,
                        scope_claims, scope_remark
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, (
                    record['application_number'],
                    clean_value(record.get('novelty_claims1')),
                    clean_value(record.get('novelty_remark1')),
                    clean_value(record.get('inventive_step_claims1')),
                    clean_value(record.get('inventive_step_remark1')),
                    clean_value(record.get('industrial_applicability_claims1')),
                    clean_value(record.get('industrial_applicability_remark1')),
                    clean_value(record.get('section_3_claims1')),
                    clean_value(record.get('section_3_remark1')),
                    clean_value(record.get('section_4_claims1')),
                    clean_value(record.get('section_4_remark1')),
                    clean_value(record.get('unity_of_invention_claims1')),
                    clean_value(record.get('unity_of_invention_remark1')),
                    clean_value(record.get('sufficiency_of_disclosure_remark1')),
                    clean_value(record.get('reference_to_foreign_applications_remark1')),
                    clean_value(record.get('clarity_claims1')),
                    clean_value(record.get('clarity_remark1')),
                    clean_value(record.get('definitive_claims1')),
                    clean_value(record.get('definitive_remark1')),
                    clean_value(record.get('supported_by_description_claims1')),
                    clean_value(record.get('supported_by_description_remark1')),
                    clean_value(record.get('scope_claims1')),
                    clean_value(record.get('scope_remark1'))
                ))

            conn.commit()
            print(f"✅ Migrated {len(backup_data)} records to new structure")
    finally:
        conn.close()

def main():
    print("🔄 Starting FER Summary migration to single-row structure...")
    
    # Step 1: Backup existing data
    backup_data = backup_existing_data()
    
    # Step 2: Create new table structure
    create_new_table()
    
    # Step 3: Migrate data
    if backup_data:
        migrate_data(backup_data)
    
    print("✅ Migration completed successfully!")
    print("📁 Backup saved to: fer_summary_backup.json")

if __name__ == "__main__":
    main()
