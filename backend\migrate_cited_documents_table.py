#!/usr/bin/env python3
"""
Migration script to create the application_cited_documents table
"""

import mysql.connector
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
db_config = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD'),
    'database': os.getenv('DB_NAME', 'fer_reply')
}

def create_cited_documents_table():
    """Create the application_cited_documents table"""
    try:
        # Connect to database
        print("🔗 Connecting to database...")
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        
        # Check if table already exists
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = 'application_cited_documents'
        """, (db_config['database'],))
        
        table_exists = cursor.fetchone()[0] > 0
        
        if table_exists:
            print("⚠️ Table 'application_cited_documents' already exists.")
            response = input("Do you want to drop and recreate it? (y/N): ").strip().lower()
            if response == 'y':
                print("🗑️ Dropping existing table...")
                cursor.execute("DROP TABLE application_cited_documents")
                print("✅ Table dropped successfully.")
            else:
                print("❌ Migration cancelled.")
                return
        
        # Create the table
        print("🏗️ Creating application_cited_documents table...")
        create_table_sql = """
        CREATE TABLE application_cited_documents (
            id INT AUTO_INCREMENT PRIMARY KEY,
            application_number VARCHAR(50) NOT NULL,

            citation_label VARCHAR(100),  -- e.g., D1, D2
            document_number VARCHAR(100), -- e.g., JP2001287542A

            publication_date DATE,        -- e.g., 2001-10-16

            relevant_description TEXT,    -- e.g., "the whole document", "claims", "abstract"
            relevant_claims_of_cited_doc VARCHAR(100), -- e.g., 1-5
            claims_of_alleged_invention  VARCHAR(100), -- e.g., 1-9

            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (application_number) REFERENCES application_examination_details(application_number) ON DELETE CASCADE
        )
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        
        print("✅ Table 'application_cited_documents' created successfully!")
        
        # Verify table structure
        cursor.execute("DESCRIBE application_cited_documents")
        columns = cursor.fetchall()
        print("\n📋 Table structure:")
        for column in columns:
            print(f"  - {column[0]}: {column[1]}")
        
        print(f"\n🎉 Migration completed successfully!")
        print("The application_cited_documents table is now ready to store cited documents data.")
        
    except mysql.connector.Error as err:
        print(f"❌ Database error: {err}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🚀 Starting migration for application_cited_documents table...")
    create_cited_documents_table()
