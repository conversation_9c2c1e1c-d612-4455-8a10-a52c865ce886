from fastapi import APIRouter, HTTPException, Depends, Body
from pydantic import BaseModel
import mysql.connector
from models.db import get_db
import os
import shutil
from typing import Optional

router = APIRouter()

UPLOADS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')

class SummaryUpdateRequest(BaseModel):
    summary: str

class ObjectionsUpdateRequest(BaseModel):
    objections: str

class ExaminationDetailsUpdateRequest(BaseModel):
    date_of_dispatch: Optional[str] = None
    to_address: Optional[str] = None
    to_email: Optional[str] = None
    date_of_filing: Optional[str] = None
    date_of_priority: Optional[str] = None
    pct_application_num_and_date: Optional[str] = None
    applicant_name: Optional[str] = None
    request_for_examination_no_and_date: Optional[str] = None
    date_of_publication: Optional[str] = None
    name_of_examinar: Optional[str] = None
    examinar_location: Optional[str] = None
    name_of_controller: Optional[str] = None
    controller_email: Optional[str] = None
    controller_location: Optional[str] = None
    due_date_of_response: Optional[str] = None

class FerSummaryUpdateRequest(BaseModel):
    # Novelty
    novelty_claims1: Optional[str] = None
    novelty_remark1: Optional[str] = None
    novelty_claims2: Optional[str] = None
    novelty_remark2: Optional[str] = None

    # Inventive Step
    inventive_step_claims1: Optional[str] = None
    inventive_step_remark1: Optional[str] = None
    inventive_step_claims2: Optional[str] = None
    inventive_step_remark2: Optional[str] = None

    # Industrial Applicability
    industrial_applicability_claims1: Optional[str] = None
    industrial_applicability_remark1: Optional[str] = None
    industrial_applicability_claims2: Optional[str] = None
    industrial_applicability_remark2: Optional[str] = None

    # Section 3
    section_3_claims1: Optional[str] = None
    section_3_remark1: Optional[str] = None
    section_3_claims2: Optional[str] = None
    section_3_remark2: Optional[str] = None

    # Section 4
    section_4_claims1: Optional[str] = None
    section_4_remark1: Optional[str] = None
    section_4_claims2: Optional[str] = None
    section_4_remark2: Optional[str] = None

    # Unity of Invention
    unity_of_invention_claims1: Optional[str] = None
    unity_of_invention_remark1: Optional[str] = None
    unity_of_invention_claims2: Optional[str] = None
    unity_of_invention_remark2: Optional[str] = None

    # Sufficiency of Disclosure
    sufficiency_of_disclosure_remark1: Optional[str] = None

    # Reference to foreign applications
    reference_to_foreign_applications_remark1: Optional[str] = None

    # Clarity
    clarity_claims1: Optional[str] = None
    clarity_remark1: Optional[str] = None
    clarity_claims2: Optional[str] = None
    clarity_remark2: Optional[str] = None

    # Definitive
    definitive_claims1: Optional[str] = None
    definitive_remark1: Optional[str] = None
    definitive_claims2: Optional[str] = None
    definitive_remark2: Optional[str] = None

    # Supported by Description
    supported_by_description_claims1: Optional[str] = None
    supported_by_description_remark1: Optional[str] = None
    supported_by_description_claims2: Optional[str] = None
    supported_by_description_remark2: Optional[str] = None

    # Scope
    scope_claims1: Optional[str] = None
    scope_remark1: Optional[str] = None
    scope_claims2: Optional[str] = None
    scope_remark2: Optional[str] = None

@router.get("/applications")
def list_applications():
    db = None
    try:
        db = get_db()
        cursor = db.cursor(dictionary=True)
        cursor.execute("SELECT application_number, applicant_name, date_of_dispatch FROM application_examination_details ORDER BY createdAt DESC")
        applications = cursor.fetchall()
        return {"applications": applications}
    except mysql.connector.Error as err:
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        if db and db.is_connected():
            cursor.close()
            db.close()

@router.get("/applications/{application_number}")
def get_application_details(application_number: str):
    db = None
    try:
        db = get_db()
        cursor = db.cursor(buffered=True, dictionary=True)

        # Get application details including summary and objections
        cursor.execute("SELECT * FROM application_examination_details WHERE application_number = %s", (application_number,))
        application = cursor.fetchone()
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")

        return application
    except mysql.connector.Error as err:
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        if db and db.is_connected():
            cursor.close()
            db.close()

@router.put("/applications/{application_number}/summary")
def update_application_summary(application_number: str, request: SummaryUpdateRequest):
    db = None
    try:
        db = get_db()
        cursor = db.cursor()

        # Update summary in application_examination_details
        cursor.execute("""
            UPDATE application_examination_details
            SET summary = %s, summary_generated_by = 'manual'
            WHERE application_number = %s
        """, (request.summary, application_number))

        if cursor.rowcount == 0:
            raise HTTPException(status_code=404, detail="Application not found")

        db.commit()
        return {"message": "Summary updated successfully"}
    except mysql.connector.Error as err:
        if db: db.rollback()
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        if db and db.is_connected():
            cursor.close()
            db.close()

@router.put("/applications/{application_number}/objections")
def update_application_objections(application_number: str, request: ObjectionsUpdateRequest):
    db = None
    try:
        db = get_db()
        cursor = db.cursor()

        # Update objections in application_examination_details
        cursor.execute("""
            UPDATE application_examination_details
            SET objection_responses = %s, objections_generated_by = 'manual'
            WHERE application_number = %s
        """, (request.objections, application_number))

        if cursor.rowcount == 0:
            raise HTTPException(status_code=404, detail="Application not found")

        db.commit()
        return {"message": "Objections updated successfully"}
    except mysql.connector.Error as err:
        if db: db.rollback()
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        if db and db.is_connected():
            cursor.close()
            db.close()

@router.delete("/applications/{application_number}")
def delete_application(application_number: str):
    db = None
    try:
        db = get_db()
        cursor = db.cursor()

        # Delete uploaded files folder
        app_folder = os.path.join(UPLOADS_DIR, application_number)
        if os.path.exists(app_folder):
            try:
                shutil.rmtree(app_folder)
            except Exception as e:
                print(f"Warning: Could not delete folder {app_folder}: {e}")

        # Delete related documents (will cascade due to foreign key)
        cursor.execute("DELETE FROM documents WHERE application_number = %s", (application_number,))

        # Delete the application examination details
        cursor.execute("DELETE FROM application_examination_details WHERE application_number = %s", (application_number,))

        if cursor.rowcount == 0:
            raise HTTPException(status_code=404, detail="Application not found")

        db.commit()
        return {"message": "Application and related data deleted successfully"}
    except mysql.connector.Error as err:
        if db: db.rollback()
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        if db and db.is_connected():
            cursor.close()
            db.close()

@router.get('/examination-details/{application_number}')
def get_examination_details(application_number: str):
    conn = get_db()
    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("SELECT * FROM application_examination_details WHERE application_number = %s", (application_number,))
            details = cursor.fetchone()
            if not details:
                raise HTTPException(status_code=404, detail="Details not found")
            return details
    finally:
        conn.close()

@router.put('/examination-details/{application_number}')
def update_examination_details(application_number: str, request: ExaminationDetailsUpdateRequest = Body(...)):
    conn = get_db()
    try:
        with conn.cursor() as cursor:
            cursor.execute("""
                UPDATE application_examination_details SET
                    date_of_dispatch=%s,
                    to_address=%s,
                    to_email=%s,
                    date_of_filing=%s,
                    date_of_priority=%s,
                    pct_application_num_and_date=%s,
                    applicant_name=%s,
                    request_for_examination_no_and_date=%s,
                    date_of_publication=%s,
                    name_of_examinar=%s,
                    examinar_location=%s,
                    name_of_controller=%s,
                    controller_email=%s,
                    controller_location=%s,
                    due_date_of_response=%s,
                    updatedAt=NOW()
                WHERE application_number=%s
            """,
            (
                request.date_of_dispatch,
                request.to_address,
                request.to_email,
                request.date_of_filing,
                request.date_of_priority,
                request.pct_application_num_and_date,
                request.applicant_name,
                request.request_for_examination_no_and_date,
                request.date_of_publication,
                request.name_of_examinar,
                request.examinar_location,
                request.name_of_controller,
                request.controller_email,
                request.controller_location,
                request.due_date_of_response,
                application_number
            ))
            conn.commit()
            return {"message": "Examination details updated successfully"}
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

@router.get('/examination-details')
def list_examination_details():
    conn = get_db()
    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("SELECT application_number, date_of_dispatch, applicant_name FROM application_examination_details ORDER BY updatedAt DESC, date_of_dispatch DESC")
            rows = cursor.fetchall()
            return {"examinations": rows}
    finally:
        conn.close()

@router.get('/documents/fer/{application_number}')
def get_fer_document_path(application_number: str):
    conn = get_db()
    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("SELECT file_path FROM documents WHERE application_number = %s AND type = 'FER'", (application_number,))
            doc = cursor.fetchone()
            # Defensive: clear any unread results
            while cursor.nextset():
                pass
            if not doc:
                raise HTTPException(status_code=404, detail="FER document not found")

            # Convert file path to URL path
            file_path = doc['file_path']
            # Extract the relative path from uploads directory
            uploads_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')
            if file_path.startswith(uploads_dir):
                relative_path = os.path.relpath(file_path, uploads_dir)
                # Convert Windows path separators to URL separators
                url_path = relative_path.replace('\\', '/')
                return {"file_path": f"/uploads/{url_path}"}
            else:
                return {"file_path": file_path}
    finally:
        conn.close()

# FER Summary endpoints
@router.get('/fer-summary/{application_number}')
def get_fer_summary(application_number: str):
    """Get FER summary data for an application"""
    conn = get_db()
    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("SELECT * FROM application_fer_summary WHERE application_number = %s", (application_number,))
            summary = cursor.fetchone()
            # Clear any unread results
            while cursor.nextset():
                pass
            if not summary:
                return {"summary": None}
            return {"summary": summary}
    except mysql.connector.Error as err:
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        conn.close()

@router.post('/fer-summary/{application_number}')
def create_fer_summary(application_number: str, summary_data: FerSummaryUpdateRequest):
    """Create or update FER summary data for an application"""
    conn = get_db()
    try:
        with conn.cursor() as cursor:
            # Check if summary already exists
            cursor.execute("SELECT id FROM application_fer_summary WHERE application_number = %s", (application_number,))
            existing = cursor.fetchone()

            # Prepare the data
            data = summary_data.dict(exclude_unset=True)

            if existing:
                # Update existing record
                if data:  # Only update if there's data to update
                    set_clause = ", ".join([f"{key} = %s" for key in data.keys()])
                    values = list(data.values()) + [application_number]
                    query = f"UPDATE application_fer_summary SET {set_clause} WHERE application_number = %s"
                    cursor.execute(query, values)
            else:
                # Insert new record
                data['application_number'] = application_number
                columns = ", ".join(data.keys())
                placeholders = ", ".join(["%s"] * len(data))
                query = f"INSERT INTO application_fer_summary ({columns}) VALUES ({placeholders})"
                cursor.execute(query, list(data.values()))

            conn.commit()
            return {"message": "FER summary saved successfully"}
    except mysql.connector.Error as err:
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        conn.close()

@router.put('/fer-summary/{application_number}')
def update_fer_summary(application_number: str, summary_data: FerSummaryUpdateRequest):
    """Update FER summary data for an application"""
    conn = get_db()
    try:
        with conn.cursor() as cursor:
            # Check if summary exists
            cursor.execute("SELECT id FROM application_fer_summary WHERE application_number = %s", (application_number,))
            existing = cursor.fetchone()

            if not existing:
                raise HTTPException(status_code=404, detail="FER summary not found")

            # Prepare the data
            data = summary_data.dict(exclude_unset=True)

            if data:  # Only update if there's data to update
                set_clause = ", ".join([f"{key} = %s" for key in data.keys()])
                values = list(data.values()) + [application_number]
                query = f"UPDATE application_fer_summary SET {set_clause} WHERE application_number = %s"
                cursor.execute(query, values)
                conn.commit()
                return {"message": "FER summary updated successfully"}
            else:
                return {"message": "No data to update"}
    except mysql.connector.Error as err:
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        conn.close()

# Cited Documents endpoints
@router.get('/cited-documents/{application_number}')
def get_cited_documents(application_number: str):
    """Get cited documents data for an application"""
    conn = get_db()
    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("SELECT * FROM application_cited_documents WHERE application_number = %s ORDER BY id", (application_number,))
            documents = cursor.fetchall()
            # Clear any unread results
            while cursor.nextset():
                pass
            return {"cited_documents": documents}
    except mysql.connector.Error as err:
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        conn.close()

class CitedDocumentRequest(BaseModel):
    citation_label: Optional[str] = None
    document_number: Optional[str] = None
    publication_date: Optional[str] = None  # YYYY-MM-DD format
    relevant_description: Optional[str] = None
    relevant_claims_of_cited_doc: Optional[str] = None
    claims_of_alleged_invention: Optional[str] = None

@router.put('/cited-documents/{application_number}')
def update_cited_documents(application_number: str, documents: list[CitedDocumentRequest]):
    """Update cited documents for an application"""
    conn = get_db()
    try:
        with conn.cursor() as cursor:
            # Delete existing cited documents
            cursor.execute("DELETE FROM application_cited_documents WHERE application_number = %s", (application_number,))

            # Insert new cited documents
            for doc in documents:
                cursor.execute("""
                    INSERT INTO application_cited_documents (
                        application_number, citation_label, document_number, publication_date,
                        relevant_description, relevant_claims_of_cited_doc, claims_of_alleged_invention
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (
                    application_number,
                    doc.citation_label,
                    doc.document_number,
                    doc.publication_date,
                    doc.relevant_description,
                    doc.relevant_claims_of_cited_doc,
                    doc.claims_of_alleged_invention
                ))

            conn.commit()
            return {"message": "Cited documents updated successfully"}
    except mysql.connector.Error as err:
        conn.rollback()
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        conn.close()