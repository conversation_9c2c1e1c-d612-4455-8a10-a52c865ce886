from fastapi import APIRouter, HTTPException, Depends, Body
from pydantic import BaseModel
import mysql.connector
from models.db import get_db
import os
import shutil
from typing import Optional

router = APIRouter()

UPLOADS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')

class SummaryUpdateRequest(BaseModel):
    summary: str

class ObjectionsUpdateRequest(BaseModel):
    objections: str

class ExaminationDetailsUpdateRequest(BaseModel):
    date_of_dispatch: Optional[str] = None
    to_address: Optional[str] = None
    to_email: Optional[str] = None
    date_of_filing: Optional[str] = None
    date_of_priority: Optional[str] = None
    pct_application_num_and_date: Optional[str] = None
    applicant_name: Optional[str] = None
    request_for_examination_no_and_date: Optional[str] = None
    date_of_publication: Optional[str] = None
    name_of_examinar: Optional[str] = None
    examinar_location: Optional[str] = None
    name_of_controller: Optional[str] = None
    controller_email: Optional[str] = None
    controller_location: Optional[str] = None
    due_date_of_response: Optional[str] = None

@router.get("/patents")
def list_patents():
    db = None
    try:
        db = get_db()
        cursor = db.cursor(dictionary=True)
        cursor.execute("SELECT id, application_number FROM patents ORDER BY created_at DESC")
        patents = cursor.fetchall()
        return {"patents": patents}
    except mysql.connector.Error as err:
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        if db and db.is_connected():
            cursor.close()
            db.close()

@router.get("/patents/{patent_id}")
def get_patent_details(patent_id: int):
    db = None
    try:
        db = get_db()
        cursor = db.cursor(buffered=True, dictionary=True)
        
        # Get application number from patents table
        cursor.execute("SELECT id, application_number FROM patents WHERE id = %s", (patent_id,))
        patent = cursor.fetchone()
        if not patent:
            raise HTTPException(status_code=404, detail="Patent not found")
        
        # Get summary from fer_replies table
        cursor.execute("SELECT content FROM fer_replies WHERE patent_id = %s AND section = 'Summary'", (patent_id,))
        summary_row = cursor.fetchone()
        patent['summary'] = summary_row['content'] if summary_row else ''
        
        # Get objections from fer_replies table
        cursor.execute("SELECT content FROM fer_replies WHERE patent_id = %s AND section = 'Objection Responses'", (patent_id,))
        objections_row = cursor.fetchone()
        patent['objections'] = objections_row['content'] if objections_row else ''
        
        return patent
    except mysql.connector.Error as err:
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        if db and db.is_connected():
            cursor.close()
            db.close()

@router.put("/patents/{patent_id}/summary")
def update_patent_summary(patent_id: int, request: SummaryUpdateRequest):
    db = None
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Upsert logic: Update if exists, otherwise insert.
        cursor.execute(
            "UPDATE fer_replies SET content = %s WHERE patent_id = %s AND section = 'Summary'",
            (request.summary, patent_id)
        )
        if cursor.rowcount == 0:
            cursor.execute(
                "INSERT INTO fer_replies (patent_id, section, content) VALUES (%s, 'Summary', %s)",
                (patent_id, request.summary)
            )
        db.commit()
        return {"message": "Summary updated successfully"}
    except mysql.connector.Error as err:
        if db: db.rollback()
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        if db and db.is_connected():
            cursor.close()
            db.close()

@router.put("/patents/{patent_id}/objections")
def update_patent_objections(patent_id: int, request: ObjectionsUpdateRequest):
    db = None
    try:
        db = get_db()
        cursor = db.cursor()
        
        # Upsert logic: Update if exists, otherwise insert.
        cursor.execute(
            "UPDATE fer_replies SET content = %s WHERE patent_id = %s AND section = 'Objection Responses'",
            (request.objections, patent_id)
        )
        if cursor.rowcount == 0:
            cursor.execute(
                "INSERT INTO fer_replies (patent_id, section, content) VALUES (%s, 'Objection Responses', %s)",
                (patent_id, request.objections)
            )
        db.commit()
        return {"message": "Objections updated successfully"}
    except mysql.connector.Error as err:
        if db: db.rollback()
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        if db and db.is_connected():
            cursor.close()
            db.close()

@router.delete("/patents/{patent_id}")
def delete_patent(patent_id: int):
    db = None
    try:
        db = get_db()
        cursor = db.cursor()
        # Get application number for this patent
        cursor.execute("SELECT application_number FROM patents WHERE id = %s", (patent_id,))
        row = cursor.fetchone()
        application_number = row[0] if row else None
        if application_number:
            app_folder = os.path.join(UPLOADS_DIR, application_number)
            if os.path.exists(app_folder):
                try:
                    shutil.rmtree(app_folder)
                except Exception as e:
                    print(f"Warning: Could not delete folder {app_folder}: {e}")
        # Delete related fer_replies
        cursor.execute("DELETE FROM fer_replies WHERE patent_id = %s", (patent_id,))
        # Delete related documents
        cursor.execute("DELETE FROM documents WHERE patent_id = %s", (patent_id,))
        # Delete the patent itself
        cursor.execute("DELETE FROM patents WHERE id = %s", (patent_id,))
        db.commit()
        return {"message": "Patent and related data deleted successfully"}
    except mysql.connector.Error as err:
        if db: db.rollback()
        raise HTTPException(status_code=500, detail=str(err))
    finally:
        if db and db.is_connected():
            cursor.close()
            db.close()

@router.get('/examination-details/{application_number}')
def get_examination_details(application_number: str):
    conn = get_db()
    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("SELECT * FROM application_examination_details WHERE application_number = %s", (application_number,))
            details = cursor.fetchone()
            if not details:
                raise HTTPException(status_code=404, detail="Details not found")
            return details
    finally:
        conn.close()

@router.put('/examination-details/{application_number}')
def update_examination_details(application_number: str, request: ExaminationDetailsUpdateRequest = Body(...)):
    conn = get_db()
    try:
        with conn.cursor() as cursor:
            cursor.execute("""
                UPDATE application_examination_details SET
                    date_of_dispatch=%s,
                    to_address=%s,
                    to_email=%s,
                    date_of_filing=%s,
                    date_of_priority=%s,
                    pct_application_num_and_date=%s,
                    applicant_name=%s,
                    request_for_examination_no_and_date=%s,
                    date_of_publication=%s,
                    name_of_examinar=%s,
                    examinar_location=%s,
                    name_of_controller=%s,
                    controller_email=%s,
                    controller_location=%s,
                    due_date_of_response=%s,
                    updatedAt=NOW()
                WHERE application_number=%s
            """,
            (
                request.date_of_dispatch,
                request.to_address,
                request.to_email,
                request.date_of_filing,
                request.date_of_priority,
                request.pct_application_num_and_date,
                request.applicant_name,
                request.request_for_examination_no_and_date,
                request.date_of_publication,
                request.name_of_examinar,
                request.examinar_location,
                request.name_of_controller,
                request.controller_email,
                request.controller_location,
                request.due_date_of_response,
                application_number
            ))
            conn.commit()
            return {"message": "Examination details updated successfully"}
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()

@router.get('/examination-details')
def list_examination_details():
    conn = get_db()
    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("SELECT application_number, date_of_dispatch, applicant_name FROM application_examination_details ORDER BY updatedAt DESC, date_of_dispatch DESC")
            rows = cursor.fetchall()
            return {"examinations": rows}
    finally:
        conn.close()

@router.get('/documents/fer/{application_number}')
def get_fer_document_path(application_number: str):
    conn = get_db()
    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("SELECT file_path FROM documents WHERE application_number = %s AND type = 'FER'", (application_number,))
            doc = cursor.fetchone()
            # Defensive: clear any unread results
            while cursor.nextset():
                pass
            if not doc:
                raise HTTPException(status_code=404, detail="FER document not found")
            return {"file_path": doc['file_path']}
    finally:
        conn.close() 