# Patent FER Reply Generator (AI-Powered)

## Overview

This system generates a patent FER (First Examination Report) reply using AI models. Users upload FER, Specification, and Claims PDFs, and the backend processes these with Ollama (local) and Gemini (Google Cloud) to generate section-wise replies. All generated content is stored in a MySQL database.

## Technology Stack

- **Frontend:** React.js (Next.js optional)
- **Backend:** Python (FastAPI or Flask preferred)
- **Database:** MySQL
- **AI Models:** Ollama (local), Google Gemini
- **PDF Parsing:** PyMuPDF (fitz), pdfplumber, or pdfminer.six

## Folder Structure

```
fer-reply-generator/
│
├── backend/
│   ├── app.py / main.py
│   ├── models/
│   ├── routes/
│   ├── utils/
│   ├── ai_modules/
│   └── uploads/
│
├── frontend/
│   ├── public/
│   ├── components/
│   ├── pages/
│   └── services/
```

## Features

- Upload FER, Specification, and Claims PDFs
- AI-powered FER summary and objection-wise reply generation
- MySQL-backed storage of documents and generated content
- Downloadable final draft

---

See the project documentation for setup and usage instructions.
