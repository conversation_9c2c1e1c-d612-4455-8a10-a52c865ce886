import fitz  # PyMuPDF
from pdfminer.high_level import extract_text
import re
from datetime import datetime

def extract_text_pymupdf(pdf_path):
    doc = fitz.open(pdf_path)
    text = ""
    for page in doc:
        text += page.get_text()
    return text

def extract_text_pdfminer(pdf_path):
    return extract_text(pdf_path)

def parse_application_examination_details(pdf_text):
    def extract(pattern, text, group=1, default=None, flags=0):
        match = re.search(pattern, text, flags)
        return match.group(group).strip() if match else default

    def extract_date(pattern, text, group=1):
        val = extract(pattern, text, group)
        if val and val != "--":
            try:
                return datetime.strptime(val, '%d-%m-%Y').date()
            except Exception:
                return None
        return None

    def extract_table_value(field_name_hindi, field_name_english, text):
        """Extract value from table structure where field name is in one column and value in another"""
        # Try multiple patterns for table structure
        patterns = [
            # Pattern for Hindi/English field name followed by value in next column/cell
            rf'{re.escape(field_name_hindi)}\s*/\s*{re.escape(field_name_english)}\s*([^\n\r]*?)(?:\n|\r|$)',
            rf'{re.escape(field_name_english)}\s*/\s*{re.escape(field_name_hindi)}\s*([^\n\r]*?)(?:\n|\r|$)',
            rf'{re.escape(field_name_english)}\s*([^\n\r]*?)(?:\n|\r|$)',
            rf'{re.escape(field_name_hindi)}\s*([^\n\r]*?)(?:\n|\r|$)',
            # Pattern for table cells separated by whitespace or tabs
            rf'{re.escape(field_name_hindi)}\s*/\s*{re.escape(field_name_english)}\s+([^\s][^\n\r]*?)(?:\s{{2,}}|\n|\r|$)',
            rf'{re.escape(field_name_english)}\s+([^\s][^\n\r]*?)(?:\s{{2,}}|\n|\r|$)',
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
            if match:
                value = match.group(1).strip()
                # Clean up the value
                value = re.sub(r'\s+', ' ', value)  # Replace multiple spaces with single space
                if value and value != "--" and value != "-":
                    return value
        return None

    data = {}

    # Application number - try multiple patterns
    data['application_number'] = (
        extract(r'Application No[/\s]*([0-9]{9,})', pdf_text) or
        extract(r'आवेदन संख्या[/\s]*Application Number[/\s]*([0-9]{9,})', pdf_text) or
        extract_table_value('आवेदन संख्या', 'Application Number', pdf_text)
    )
    data['date_of_dispatch'] = extract_date(r'Date of Dispatch/Email:?\s*([0-9]{2}-[0-9]{2}-[0-9]{4})', pdf_text)
    data['to_address'] = extract(r'सेवा मे,/To\s*\n([\s\S]+?)(?=\s*Email)', pdf_text, 1)
    data['to_email'] = extract(r'Email\s*:\s*([\w\.-]+@[\w\.-]+)', pdf_text)
    data['name_of_examinar'] = extract(r'Name of the Examiner:?\s*([^\n]+)', pdf_text)
    data['examinar_location'] = extract(r'Examiner Location:?\s*([^\n]+)', pdf_text)
    data['name_of_controller'] = extract(r'Name of the Controller:?\s*([^\n]+)', pdf_text)
    data['controller_email'] = extract(r'Controller\'s Email:?\s*([^\s]+)', pdf_text)
    data['controller_location'] = extract(r'Controller Location:?\s*([^\n]+)', pdf_text)
    data['due_date_of_response'] = extract_date(r'Last date for filing response to the Examination Report:?\s*([0-9]{2}-[0-9]{2}-[0-9]{4})', pdf_text)

    # Fields in table structure - use improved extraction
    date_of_filing_raw = extract_table_value('दाखिल करने की तिथि', 'Date of Filing', pdf_text)
    if date_of_filing_raw and date_of_filing_raw != "--":
        try:
            data['date_of_filing'] = datetime.strptime(date_of_filing_raw, '%d-%m-%Y').date()
        except Exception:
            data['date_of_filing'] = None
    else:
        data['date_of_filing'] = None

    date_of_priority_raw = extract_table_value('प्राथमिकता दिनांक', 'Date of Priority', pdf_text)
    if date_of_priority_raw and date_of_priority_raw != "--":
        try:
            data['date_of_priority'] = datetime.strptime(date_of_priority_raw, '%d-%m-%Y').date()
        except Exception:
            data['date_of_priority'] = None
    else:
        data['date_of_priority'] = None

    data['pct_application_num_and_date'] = extract_table_value('पीसीटी अंतर्राष्ट्रीय आवेदन की संख्या व दिनांक', 'PCT International Application No. & Date', pdf_text)

    data['applicant_name'] = extract_table_value('आवेदक', 'Applicant', pdf_text)

    data['request_for_examination_no_and_date'] = extract_table_value('परीक्षण हेतु अनुरोध की संख्या व दिनांक', 'Request for Examination No. & Date', pdf_text)

    date_of_publication_raw = extract_table_value('प्रकाशन की तिथि', 'Date of Publication', pdf_text)
    if date_of_publication_raw and date_of_publication_raw != "--":
        try:
            data['date_of_publication'] = datetime.strptime(date_of_publication_raw, '%d-%m-%Y').date()
        except Exception:
            data['date_of_publication'] = None
    else:
        data['date_of_publication'] = None

    # Clean up values and handle "--" as null
    for key, value in data.items():
        if isinstance(value, str):
            if value == "--" or value == "-" or value.strip() == "":
                data[key] = None
            else:
                # Clean up text values
                data[key] = re.sub(r'\s+', ' ', value).strip()

    # Additional cleanup for specific fields
    for k in ['applicant_name', 'name_of_examinar', 'name_of_controller']:
        if data.get(k):
            data[k] = data[k].replace('\n', ' ').replace('पर', '').replace('Controller', '').strip()

    for k in ['examinar_location', 'controller_location']:
        if data.get(k):
            data[k] = data[k].replace('\n', ' ').strip()

    return data