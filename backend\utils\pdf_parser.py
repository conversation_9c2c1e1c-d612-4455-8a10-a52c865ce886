import fitz  # PyMuPDF
from pdfminer.high_level import extract_text
import re
import json
from datetime import datetime
import google.generativeai as genai
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Gemini API
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)

def extract_text_pymupdf(pdf_path):
    doc = fitz.open(pdf_path)
    text = ""
    for page in doc:
        text += page.get_text()
    return text

def extract_text_pdfminer(pdf_path):
    return extract_text(pdf_path)

def parse_application_examination_details_with_gemini(pdf_text):
    """
    Use Gemini API to intelligently extract structured data from FER PDF text
    """
    if not GEMINI_API_KEY:
        print("⚠️ Warning: GEMINI_API_KEY not found. Falling back to regex parsing.")
        return parse_application_examination_details(pdf_text)

    try:
        # Create a structured prompt for Gemini
        prompt = f"""
You are an expert at extracting structured data from Indian Patent Office First Examination Report (FER) documents.

Please extract the following information from the FER document text below and return it as a JSON object with these exact field names:

{{
    "application_number": "string - the application number (e.g., ************)",
    "date_of_dispatch": "string - date in DD-MM-YYYY format or null",
    "to_address": "string - the complete address or null",
    "to_email": "string - email address or null",
    "name_of_examinar": "string - examiner's name or null",
    "examinar_location": "string - examiner's location or null",
    "name_of_controller": "string - controller's name or null",
    "controller_email": "string - controller's email or null",
    "controller_location": "string - controller's location or null",
    "due_date_of_response": "string - due date in DD-MM-YYYY format or null",
    "date_of_filing": "string - filing date in DD-MM-YYYY format or null",
    "date_of_priority": "string - priority date in DD-MM-YYYY format or null",
    "pct_application_num_and_date": "string - PCT application number and date or null",
    "applicant_name": "string - applicant name or null",
    "request_for_examination_no_and_date": "string - examination request number and date or null",
    "date_of_publication": "string - publication date in DD-MM-YYYY format or null"
}}

Important instructions:
1. Return ONLY the JSON object, no other text
2. Use null for missing or unavailable fields (represented by "--" or similar)
3. Extract actual values, not field names
4. For dates, use DD-MM-YYYY format
5. Be careful to distinguish between field names and actual values

FER Document Text:
{pdf_text}
"""

        # Call Gemini API
        model = genai.GenerativeModel('gemini-2.0-flash')
        response = model.generate_content(prompt)

        # Parse the JSON response (handle markdown code blocks)
        try:
            response_text = response.text.strip()

            # Remove markdown code blocks if present
            if response_text.startswith('```json'):
                response_text = response_text[7:]  # Remove ```json
            if response_text.startswith('```'):
                response_text = response_text[3:]   # Remove ```
            if response_text.endswith('```'):
                response_text = response_text[:-3]  # Remove trailing ```

            response_text = response_text.strip()
            extracted_data = json.loads(response_text)

            # Convert date strings to date objects
            date_fields = ['date_of_dispatch', 'due_date_of_response', 'date_of_filing',
                          'date_of_priority', 'date_of_publication']

            for field in date_fields:
                if extracted_data.get(field) and extracted_data[field] != "null":
                    try:
                        extracted_data[field] = datetime.strptime(extracted_data[field], '%d-%m-%Y').date()
                    except (ValueError, TypeError):
                        extracted_data[field] = None
                else:
                    extracted_data[field] = None

            print("✅ Successfully extracted data using Gemini API")
            return extracted_data

        except json.JSONDecodeError as e:
            print(f"⚠️ Error parsing Gemini JSON response: {e}")
            print(f"Raw response: {response.text[:500]}...")  # Limit output length
            # Fall back to regex parsing
            print("🔄 Falling back to regex-based parsing...")
            return parse_application_examination_details(pdf_text)

    except Exception as e:
        print(f"⚠️ Error using Gemini API: {e}")
        # Fall back to regex parsing
        print("🔄 Falling back to regex-based parsing...")
        return parse_application_examination_details(pdf_text)

def parse_application_examination_details(pdf_text):
    def extract(pattern, text, group=1, default=None, flags=0):
        match = re.search(pattern, text, flags)
        return match.group(group).strip() if match else default

    def extract_date(pattern, text, group=1):
        val = extract(pattern, text, group)
        if val and val != "--":
            try:
                return datetime.strptime(val, '%d-%m-%Y').date()
            except Exception:
                return None
        return None

    def extract_table_value(field_name_hindi, field_name_english, text):
        """Extract value from table structure where field name is in one column and value in another"""
        # Create a more flexible pattern that handles various separators
        # Look for the field name followed by any whitespace/separator and then the value

        # First, try to find lines that contain both the field name and some content after it
        lines = text.split('\n')
        for line in lines:
            # Check if this line contains our field name
            if field_name_hindi in line and field_name_english in line:
                # Try to extract the value part after the field name
                patterns = [
                    # Pattern 1: Field name followed by tab/spaces and value
                    rf'{re.escape(field_name_hindi)}\s*/\s*{re.escape(field_name_english)}\s+(.+?)$',
                    rf'{re.escape(field_name_english)}\s*/\s*{re.escape(field_name_hindi)}\s+(.+?)$',
                    # Pattern 2: More flexible - any whitespace after field name
                    rf'{re.escape(field_name_hindi)}.*?{re.escape(field_name_english)}\s+(.+?)$',
                    rf'{re.escape(field_name_english)}.*?{re.escape(field_name_hindi)}\s+(.+?)$',
                ]

                for pattern in patterns:
                    match = re.search(pattern, line.strip(), re.IGNORECASE)
                    if match:
                        value = match.group(1).strip()
                        # Clean up the value and validate it's not a field name
                        value = re.sub(r'\s+', ' ', value)  # Replace multiple spaces with single space

                        # Check if the extracted value looks like another field name
                        if (value and value != "--" and value != "-" and
                            not re.search(r'[/].*[a-zA-Z].*[/]', value) and  # Avoid field name patterns
                            not re.search(r'आवेदक|परीक्षण|प्रकाशन|दाखिल|प्राथमिकता|संख्या', value) and  # Avoid Hindi field names
                            len(value) > 1):  # Ensure it's not just a single character
                            return value
        return None

    data = {}

    # Application number - try multiple patterns
    app_num_1 = extract(r'Application No[/\s]*([0-9]{9,})', pdf_text)
    app_num_2 = extract(r'आवेदन संख्या.*?Application Number\s+([0-9]{9,})', pdf_text, 1, None, re.MULTILINE)
    app_num_3 = extract_table_value('आवेदन संख्या', 'Application Number', pdf_text)
    data['application_number'] = app_num_1 or app_num_2 or app_num_3
    data['date_of_dispatch'] = extract_date(r'Date of Dispatch/Email:?\s*([0-9]{2}-[0-9]{2}-[0-9]{4})', pdf_text)
    data['to_address'] = extract(r'सेवा मे,/To\s*\n([\s\S]+?)(?=\s*Email)', pdf_text, 1)
    data['to_email'] = extract(r'Email\s*:\s*([\w\.-]+@[\w\.-]+)', pdf_text)
    data['name_of_examinar'] = extract(r'Name of the Examiner:?\s*([^\n]+)', pdf_text)
    data['examinar_location'] = extract(r'Examiner Location:?\s*([^\n]+)', pdf_text)
    data['name_of_controller'] = extract(r'Name of the Controller:?\s*([^\n]+)', pdf_text)
    data['controller_email'] = extract(r'Controller\'s Email:?\s*([^\s]+)', pdf_text)
    data['controller_location'] = extract(r'Controller Location:?\s*([^\n]+)', pdf_text)
    data['due_date_of_response'] = extract_date(r'Last date for filing response to the Examination Report:?\s*([0-9]{2}-[0-9]{2}-[0-9]{4})', pdf_text)

    # Fields in table structure - use improved extraction
    date_of_filing_raw = extract_table_value('दाखिल करने की तिथि', 'Date of Filing', pdf_text)
    if date_of_filing_raw and date_of_filing_raw != "--":
        try:
            data['date_of_filing'] = datetime.strptime(date_of_filing_raw, '%d-%m-%Y').date()
        except Exception:
            data['date_of_filing'] = None
    else:
        data['date_of_filing'] = None

    date_of_priority_raw = extract_table_value('प्राथमिकता दिनांक', 'Date of Priority', pdf_text)
    if date_of_priority_raw and date_of_priority_raw != "--":
        try:
            data['date_of_priority'] = datetime.strptime(date_of_priority_raw, '%d-%m-%Y').date()
        except Exception:
            data['date_of_priority'] = None
    else:
        data['date_of_priority'] = None

    data['pct_application_num_and_date'] = extract_table_value('पीसीटी अंतर्राष्ट्रीय आवेदन की संख्या व दिनांक', 'PCT International Application No. & Date', pdf_text)

    data['applicant_name'] = extract_table_value('आवेदक', 'Applicant', pdf_text)

    data['request_for_examination_no_and_date'] = extract_table_value('परीक्षण हेतु अनुरोध की संख्या व दिनांक', 'Request for Examination No. & Date', pdf_text)

    date_of_publication_raw = extract_table_value('प्रकाशन की तिथि', 'Date of Publication', pdf_text)
    if date_of_publication_raw and date_of_publication_raw != "--":
        try:
            data['date_of_publication'] = datetime.strptime(date_of_publication_raw, '%d-%m-%Y').date()
        except Exception:
            data['date_of_publication'] = None
    else:
        data['date_of_publication'] = None

    # Clean up values and handle "--" as null
    for key, value in data.items():
        if isinstance(value, str):
            if value == "--" or value == "-" or value.strip() == "":
                data[key] = None
            else:
                # Clean up text values
                data[key] = re.sub(r'\s+', ' ', value).strip()

    # Additional cleanup for specific fields
    for k in ['applicant_name', 'name_of_examinar', 'name_of_controller']:
        if data.get(k):
            data[k] = data[k].replace('\n', ' ').replace('पर', '').replace('Controller', '').strip()

    for k in ['examinar_location', 'controller_location']:
        if data.get(k):
            data[k] = data[k].replace('\n', ' ').strip()

    return data

def parse_fer_summary_with_gemini(pdf_text):
    """
    Use Gemini API to extract FER summary data from Part-1 of the FER document
    Handles multiple rows per requirement (claims1/remark1, claims2/remark2)
    """
    if not GEMINI_API_KEY:
        print("⚠️ Warning: GEMINI_API_KEY not found. Cannot parse FER summary.")
        return None

    try:
        # Create a structured prompt for Gemini to extract summary data with multiple rows
        prompt = f"""
You are an expert at extracting structured data from Indian Patent Office First Examination Report (FER) documents.

Please extract the summary data from "PART-I: SUMMARY OF THE REPORT" section of the FER document text below.

IMPORTANT: Each requirement may have MULTIPLE ROWS in the table. Extract ALL rows for each requirement.

The summary table has the following structure:
- Column 1: Sr. No.
- Column 2: Requirements under the Act (in Hindi/English)
- Column 3: Claim Numbers (दावे/Claims)
- Column 4: Remarks (टिप्पणी/Remarks)

Look for these specific requirements and extract ALL rows for each:

1. Novelty (नवीनता/Novelty)
2. Inventive step (आविष्कारिक कदम/Inventive step)
3. Industrial Applicability (औद्योगिक उपयोगिता/Industrial Applicability)
4. Nonpatentability u/s 3 (धारा 3 के अंतर्गत अपेटेंटेबिलिटी/Non-patentability u/s 3)
5. Non-patentability u/s 4 (धारा 4 के अंतर्गत अपेटेंटेबिलिटी/Non-patentability u/s 4)
6. Unity of invention u/s 10(5) (धारा 10(5) के अंतर्गत आविष्कार की एकता/Unity of invention u/s 10(5))
7. Sufficiency of disclosure u/s 10(4) (धारा 10(4) के अंतर्गत पर्याप्त प्रकटीकरण/Sufficiency of disclosure u/s 10(4))
8. Reference to co-pending/foreign applications (सह-लंबित/विदेशी आवेदन का संदर्भ/Reference to co-pending/foreign application(s))
9. Clarity/Conciseness (स्पष्टता/स्पष्टता/Clarity/Conciseness)
10. Definitive (निश्चित/Definitive)
11. Supported by description (विवरण द्वारा समर्थित/Supported by description)
12. Scope (क्षेत्र/Scope)

For each requirement, extract up to 2 rows:
- Row 1: claims1 and remark1
- Row 2: claims2 and remark2 (if exists)

Extract claim numbers exactly as they appear (e.g., "1-28", "1-10", "NONE", empty string)
Extract remarks exactly as they appear from the summary table ONLY. Remarks should be short:
- "Yes", "No", "Yes 3(d)", "हाँ", "नहीं", etc.
- Do NOT extract long explanatory text from other parts of the document
- Only extract the actual remark value from the "Remarks" column of the summary table

Convert Hindi remarks to English but preserve additional text:
- "हाँ" → "Yes"
- "नहीं" → "No"
- "हाँ 3(d)" → "Yes 3(d)"

IMPORTANT: Focus ONLY on the summary table in "PART-I: SUMMARY OF THE REPORT" section.
Do not extract detailed explanations or long text from other sections of the document.
- Keep complex remarks like "Yes 3(d)" as is

Return as JSON with these exact field names (use empty string "" for missing values):
Remarks should be SHORT (e.g., "Yes", "No", "Yes 3(d)") - NOT long explanatory text.

{{
    "novelty_claims1": "claim_numbers_like_1-10",
    "novelty_remark1": "short_remark_like_Yes_or_No",
    "novelty_claims2": "claim_numbers_or_NONE",
    "novelty_remark2": "short_remark_like_Yes_or_No",
    "inventive_step_claims1": "claim_numbers_like_1-10",
    "inventive_step_remark1": "short_remark_like_Yes_or_No",
    "inventive_step_claims2": "claim_numbers_or_NONE",
    "inventive_step_remark2": "short_remark_like_Yes_or_No",
    "industrial_applicability_claims1": "claim_numbers_like_1-10",
    "industrial_applicability_remark1": "short_remark_like_Yes_or_No",
    "industrial_applicability_claims2": "claim_numbers_or_NONE",
    "industrial_applicability_remark2": "short_remark_like_Yes_or_No",
    "section_3_claims1": "claim_numbers_like_1-10",
    "section_3_remark1": "short_remark_like_Yes_3d",
    "section_3_claims2": "claim_numbers_or_NONE",
    "section_3_remark2": "short_remark_like_Yes_or_No",
    "section_4_claims1": "claim_numbers_like_1-10",
    "section_4_remark1": "short_remark_like_Yes_or_No",
    "section_4_claims2": "claim_numbers_or_NONE",
    "section_4_remark2": "short_remark_like_Yes_or_No",
    "unity_of_invention_claims1": "claim_numbers_like_1-10",
    "unity_of_invention_remark1": "short_remark_like_Yes_or_No",
    "unity_of_invention_claims2": "claim_numbers_or_NONE",
    "unity_of_invention_remark2": "short_remark_like_Yes_or_No",
    "sufficiency_of_disclosure_remark1": "short_remark_like_YES_or_No",
    "reference_to_foreign_applications_remark1": "short_remark_like_No_or_Yes",
    "clarity_claims1": "first_row_claim_numbers",
    "clarity_remark1": "first_row_remark",
    "clarity_claims2": "second_row_claim_numbers",
    "clarity_remark2": "second_row_remark",
    "definitive_claims1": "first_row_claim_numbers",
    "definitive_remark1": "first_row_remark",
    "definitive_claims2": "second_row_claim_numbers",
    "definitive_remark2": "second_row_remark",
    "supported_by_description_claims1": "first_row_claim_numbers",
    "supported_by_description_remark1": "first_row_remark",
    "supported_by_description_claims2": "second_row_claim_numbers",
    "supported_by_description_remark2": "second_row_remark",
    "scope_claims1": "first_row_claim_numbers",
    "scope_remark1": "first_row_remark",
    "scope_claims2": "second_row_claim_numbers",
    "scope_remark2": "second_row_remark"
}}

IMPORTANT INSTRUCTIONS:
========================
1. Return ONLY the JSON object — no headings, comments, or explanations.
2. Use EMPTY STRING "" for any missing or unavailable field.
3. For **claim numbers**, extract them EXACTLY as written in the table (e.g., "1-28", "1-10", "NONE", or blank "").
4. For **remarks**, extract ONLY the value from the "Remarks" column. Keep it SHORT — do NOT extract explanations. Allowed values include:
   - "Yes"
   - "No"
   - "Yes 3(d)", "No 3(k)", etc.
   - "" (empty string if blank)
5. Do NOT extract long commentary or detailed reasoning from other sections of the FER. Focus ONLY on the summary table content.
6. Each requirement may have UP TO TWO rows. Extract both rows if present:
   - Use `requirement_claims1`, `requirement_remark1` for the first row
   - Use `requirement_claims2`, `requirement_remark2` for the second row
7. Some requirements (like "Sufficiency of Disclosure") may have only a remark and NO claim numbers. In that case:
   - Fill the `*_remark1` field with the remark (e.g., "YES")
   - Set the corresponding `*_claims1` field to empty string ""
8. Translate Hindi remarks into English while preserving their meaning:
   - "हाँ" → "Yes"
   - "नहीं" → "No"
   - "हाँ 3(d)" → "Yes 3(d)"
9. Claim Numbers and Remarks MUST match the same row in the table. Do not mismatch.
10. DO NOT guess or invent data. Only extract what's actually written in the summary table.
11. Do not include extra metadata or wrap the JSON in markdown code blocks. Just output raw JSON.
12. Ensure the final output is valid JSON and can be parsed using json.loads()

FER Document Text:
{pdf_text}
"""

        # Call Gemini API
        model = genai.GenerativeModel('gemini-2.0-flash')
        response = model.generate_content(prompt)

        # Parse the JSON response
        try:
            response_text = response.text.strip()

            # Remove markdown code blocks if present
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.startswith('```'):
                response_text = response_text[3:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]

            response_text = response_text.strip()
            extracted_data = json.loads(response_text)

            print("✅ Successfully extracted FER summary data using Gemini API")
            return extracted_data

        except json.JSONDecodeError as e:
            print(f"⚠️ Error parsing Gemini JSON response for FER summary: {e}")
            print(f"Raw response: {response.text[:500]}...")
            return None

    except Exception as e:
        print(f"⚠️ Error using Gemini API for FER summary: {e}")
        return None