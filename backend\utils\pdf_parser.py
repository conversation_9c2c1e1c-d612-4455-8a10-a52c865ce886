import fitz  # PyMuPDF
from pdfminer.high_level import extract_text
import re
from datetime import datetime

def extract_text_pymupdf(pdf_path):
    doc = fitz.open(pdf_path)
    text = ""
    for page in doc:
        text += page.get_text()
    return text

def extract_text_pdfminer(pdf_path):
    return extract_text(pdf_path)

def parse_application_examination_details(pdf_text):
    def extract(pattern, text, group=1, default=None, flags=0):
        match = re.search(pattern, text, flags)
        return match.group(group).strip() if match else default

    def extract_date(pattern, text, group=1):
        val = extract(pattern, text, group)
        if val:
            try:
                return datetime.strptime(val, '%d-%m-%Y').date()
            except Exception:
                return None
        return None

    data = {}
    data['application_number'] = extract(r'Application No/?\s*:? ?([0-9]{9,})', pdf_text)
    data['date_of_dispatch'] = extract_date(r'Date of Dispatch/Email:?\s*([0-9]{2}-[0-9]{2}-[0-9]{4})', pdf_text)
    data['to_address'] = extract(r'सेवा मे,/To\s*([\s\S]+?)\nEmail', pdf_text, 1)
    data['to_email'] = extract(r'Email\s*:\s*([\w\.-]+@[\w\.-]+)', pdf_text)
    data['date_of_filing'] = extract(r'Date of Filing\s*[:\-]?\s*([0-9]{2}-[0-9]{2}-[0-9]{4}|--)', pdf_text)
    data['date_of_priority'] = extract(r'Date of Priority\s*[:\-]?\s*([0-9]{2}-[0-9]{2}-[0-9]{4}|--)', pdf_text)
    data['pct_application_num_and_date'] = extract(r'PCT International Application No\.? & Date\s*[:\-]?\s*([^\n]*)', pdf_text)
    data['applicant_name'] = extract(r'Applicant\s*[:\-]?\s*([^\n,]+(?:, [^\n,]+)*)', pdf_text)
    data['request_for_examination_no_and_date'] = extract(r'Request for Examination No\.? & Date\s*[:\-]?\s*([^\n]*)', pdf_text)
    data['date_of_publication'] = extract(r'Date of Publication\s*[:\-]?\s*([0-9]{2}-[0-9]{2}-[0-9]{4}|--)', pdf_text)
    data['name_of_examinar'] = extract(r'Name of the Examiner:?\s*([^\n]+)', pdf_text)
    data['examinar_location'] = extract(r'Examiner Location:?\s*([^\n]+)', pdf_text)
    data['name_of_controller'] = extract(r'Name of the Controller:?\s*([^\n]+)', pdf_text)
    data['controller_email'] = extract(r'Controller\'s Email:?\s*([^\s]+)', pdf_text)
    data['controller_location'] = extract(r'Controller Location:?\s*([^\n]+)', pdf_text)
    data['due_date_of_response'] = extract_date(r'Last date for filing response to the Examination Report:?\s*([0-9]{2}-[0-9]{2}-[0-9]{4})', pdf_text)

    # Clean up values
    for k in ['applicant_name', 'name_of_examinar', 'name_of_controller']:
        if data.get(k):
            data[k] = data[k].replace('\n', ' ').replace('पर', '').replace('Controller', '').strip()
    for k in ['examinar_location', 'controller_location']:
        if data.get(k):
            data[k] = data[k].replace('\n', ' ').strip()
    return data 