#!/usr/bin/env python3
"""
Create test FER summary data
"""

import mysql.connector
from models.db import get_db

def create_test_data():
    """Create test FER summary data"""
    conn = get_db()
    try:
        with conn.cursor() as cursor:
            # Insert test data
            cursor.execute("""
                INSERT INTO application_fer_summary (
                    application_number,
                    novelty_claims1, novelty_remark1, novelty_claims2, novelty_remark2,
                    inventive_step_claims1, inventive_step_remark1, inventive_step_claims2, inventive_step_remark2,
                    industrial_applicability_claims1, industrial_applicability_remark1, industrial_applicability_claims2, industrial_applicability_remark2,
                    section_3_claims1, section_3_remark1, section_3_claims2, section_3_remark2,
                    section_4_claims1, section_4_remark1, section_4_claims2, section_4_remark2,
                    unity_of_invention_claims1, unity_of_invention_remark1, unity_of_invention_claims2, unity_of_invention_remark2,
                    sufficiency_of_disclosure_remark1,
                    reference_to_foreign_applications_remark1,
                    clarity_claims1, clarity_remark1, clarity_claims2, clarity_remark2,
                    definitive_claims1, definitive_remark1, definitive_claims2, definitive_remark2,
                    supported_by_description_claims1, supported_by_description_remark1, supported_by_description_claims2, supported_by_description_remark2,
                    scope_claims1, scope_remark1, scope_claims2, scope_remark2
                ) VALUES (
                    '202441030340',
                    '1-10', 'Yes', 'NONE', 'No',
                    'NONE', 'Yes', '1-10', 'No',
                    'NONE', 'Yes', '1-10', 'No',
                    'NONE', 'Yes 3(d)', '1-10', 'No',
                    '', '', '', '',
                    '', '', '', '',
                    'Yes',
                    '',
                    '10', 'Yes', '', 'No',
                    '', '', '', '',
                    '1-10', 'Yes', '', 'No',
                    '', '', '', ''
                ) ON DUPLICATE KEY UPDATE
                    novelty_claims1 = VALUES(novelty_claims1),
                    novelty_remark1 = VALUES(novelty_remark1)
            """)
            
            conn.commit()
            print("✅ Created test FER summary data for application 202441030340")
    finally:
        conn.close()

if __name__ == "__main__":
    create_test_data()
