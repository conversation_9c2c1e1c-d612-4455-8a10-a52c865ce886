#!/usr/bin/env python3
"""
Test script for cited documents parsing functionality
"""

import os
from utils.pdf_parser import extract_text_pdfminer, parse_cited_documents_with_gemini

def test_cited_documents_parsing():
    """Test the cited documents parsing with an existing PDF"""
    
    # Find a test PDF file
    uploads_dir = "uploads"
    test_pdf = None
    
    for app_folder in os.listdir(uploads_dir):
        app_path = os.path.join(uploads_dir, app_folder)
        if os.path.isdir(app_path):
            for file in os.listdir(app_path):
                if file.endswith('.pdf'):
                    test_pdf = os.path.join(app_path, file)
                    break
            if test_pdf:
                break
    
    if not test_pdf:
        print("❌ No PDF files found for testing")
        return
    
    print(f"🧪 Testing cited documents parsing with: {test_pdf}")
    
    try:
        # Extract text from PDF
        print("📄 Extracting text from PDF...")
        pdf_text = extract_text_pdfminer(test_pdf)
        print(f"✅ Extracted {len(pdf_text)} characters of text")
        
        # Parse cited documents
        print("🔍 Parsing cited documents...")
        cited_documents = parse_cited_documents_with_gemini(pdf_text)
        
        if cited_documents:
            print(f"✅ Successfully parsed {len(cited_documents)} cited documents:")
            for i, doc in enumerate(cited_documents, 1):
                print(f"\n📋 Document {i}:")
                print(f"  Citation Label: {doc.get('citation_label', 'N/A')}")
                print(f"  Document Number: {doc.get('document_number', 'N/A')}")
                print(f"  Publication Date: {doc.get('publication_date', 'N/A')}")
                print(f"  Relevant Description: {doc.get('relevant_description', 'N/A')}")
                print(f"  Relevant Claims: {doc.get('relevant_claims_of_cited_doc', 'N/A')}")
                print(f"  Claims of Invention: {doc.get('claims_of_alleged_invention', 'N/A')}")
        else:
            print("⚠️ No cited documents found or parsing failed")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")

if __name__ == "__main__":
    print("🚀 Starting cited documents parsing test...")
    test_cited_documents_parsing()
    print("🏁 Test completed!")
