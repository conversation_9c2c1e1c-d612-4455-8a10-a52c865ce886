#!/usr/bin/env python3

import re
from datetime import datetime

def extract(pattern, text, group=1, default=None, flags=0):
    match = re.search(pattern, text, flags)
    return match.group(group).strip() if match else default

def extract_table_value(field_name_hindi, field_name_english, text):
    """Extract value from table structure where field name is in one column and value in another"""
    lines = text.split('\n')
    for line in lines:
        if field_name_hindi in line and field_name_english in line:
            patterns = [
                rf'{re.escape(field_name_hindi)}\s*/\s*{re.escape(field_name_english)}\s+(.+?)$',
                rf'{re.escape(field_name_english)}\s*/\s*{re.escape(field_name_hindi)}\s+(.+?)$',
                rf'{re.escape(field_name_hindi)}.*?{re.escape(field_name_english)}\s+(.+?)$',
                rf'{re.escape(field_name_english)}.*?{re.escape(field_name_hindi)}\s+(.+?)$',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, line.strip(), re.IGNORECASE)
                if match:
                    value = match.group(1).strip()
                    value = re.sub(r'\s+', ' ', value)
                    if (value and value != "--" and value != "-" and 
                        not re.search(r'[/].*[a-zA-Z].*[/]', value) and
                        not re.search(r'आवेदक|परीक्षण|प्रकाशन|दाखिल|प्राथमिकता|संख्या', value) and
                        len(value) > 1):
                        return value
    return None

def test_parse_application_examination_details(pdf_text):
    data = {}
    
    # Application number - try multiple patterns
    data['application_number'] = (
        extract(r'Application No[/\s]*([0-9]{9,})', pdf_text) or
        extract(r'आवेदन संख्या.*?Application Number\s+([0-9]{9,})', pdf_text, 1, None, re.MULTILINE) or
        extract_table_value('आवेदन संख्या', 'Application Number', pdf_text)
    )
    
    # Table fields
    data['applicant_name'] = extract_table_value('आवेदक', 'Applicant', pdf_text)
    
    date_of_filing_raw = extract_table_value('दाखिल करने की तिथि', 'Date of Filing', pdf_text)
    if date_of_filing_raw and date_of_filing_raw != "--":
        try:
            data['date_of_filing'] = datetime.strptime(date_of_filing_raw, '%d-%m-%Y').date()
        except Exception:
            data['date_of_filing'] = None
    else:
        data['date_of_filing'] = None
    
    data['request_for_examination_no_and_date'] = extract_table_value('परीक्षण हेतु अनुरोध की संख्या व दिनांक', 'Request for Examination No. & Date', pdf_text)
    
    return data

if __name__ == "__main__":
    test_text = '''
परीक्षण रिपोर्ट /Examination Report

आवेदन संख्या /Application Number ************
दाखिल करने की तिथि /Date of Filing 15-08-2022
प्राथमिकता दिनांक /Date of Priority --
आवेदक /Applicant INDIAN INSTITUTE OF TECHNOLOGY GANDHINAGAR
परीक्षण हेतु अनुरोध की संख्या व दिनांक /Request for Examination No. & Date E202220530015-15-08-2022
प्रकाशन की तिथि /Date of Publication 25-11-2022
'''

    result = test_parse_application_examination_details(test_text)
    print('🧪 Test parser results:')
    for key, value in result.items():
        print(f'  {key}: {repr(value)}')
