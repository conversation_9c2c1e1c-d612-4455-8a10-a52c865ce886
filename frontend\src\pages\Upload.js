import React, { useRef, useState, useEffect } from "react";
import toast from "react-hot-toast";
import {
  getExaminationDetails,
  uploadDocuments,
  updateExaminationDetails,
  getFerDocumentPath,
} from "../services/api";

export default function Upload({ selectedApplication }) {
  const [ferFile, setFerFile] = useState(null);
  const [pdfUrl, setPdfUrl] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [parsedData, setParsedData] = useState(null);
  const [editData, setEditData] = useState(null);
  const fileInputRef = useRef();

  // Load PDF and parsed data when a chat is selected from the sidebar
  useEffect(() => {
    const fetchData = async () => {
      if (selectedApplication) {
        try {
          const details = await getExaminationDetails(selectedApplication);
          setParsedData(details);
          setEditData(details);
          // Fetch the FER PDF file path from backend
          const docRes = await getFerDocumentPath(selectedApplication);
          setPdfUrl(
            docRes.file_path ? docRes.file_path.replace(/^\.\//, "/") : null
          );
          setFerFile(null);
        } catch (error) {
          toast.error("Failed to load examination details.");
        }
      } else {
        setParsedData(null);
        setEditData(null);
        setPdfUrl(null);
        setFerFile(null);
      }
    };
    fetchData();
  }, [selectedApplication]);

  // Handle file selection
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.type === "application/pdf") {
      setFerFile(file);
      setPdfUrl(URL.createObjectURL(file));
      setParsedData(null);
    } else {
      toast.error("Please select a valid PDF file.");
      setFerFile(null);
      setPdfUrl(null);
    }
  };

  // Handle process button click
  const handleProcess = async () => {
    if (!ferFile) {
      toast.error("Please select a FER PDF to process.");
      return;
    }
    setIsProcessing(true);
    setParsedData(null);
    try {
      const formData = new FormData();
      formData.append("fer", ferFile);
      // Optionally add spec/claims if needed
      const uploadRes = await uploadDocuments(formData);
      // Fetch parsed details using the application_number returned from backend
      const details = await getExaminationDetails(uploadRes.application_number);
      setParsedData(details);
      setEditData(details);
      setIsProcessing(false);
      toast.success("FER processed and details parsed!");
    } catch (error) {
      toast.error("Failed to process FER PDF.");
      setIsProcessing(false);
    }
  };

  // Handle input change for editable fields
  const handleEditChange = (key, value) => {
    setEditData((prev) => ({ ...prev, [key]: value }));
  };

  // Normalize data before sending to backend
  const normalizeEditData = (data) => {
    const normalized = {};
    Object.entries(data).forEach(([key, value]) => {
      if (key === "application_number") return; // Don't send this
      normalized[key] =
        value === ""
          ? null
          : value !== null && value !== undefined
          ? value.toString()
          : null;
    });
    return normalized;
  };

  // Handle save changes
  const handleSave = async () => {
    if (!editData || !editData.application_number) return;
    try {
      const normalized = normalizeEditData(editData);
      await updateExaminationDetails(editData.application_number, normalized);
      toast.success("Examination details updated successfully!");
      // Optionally refetch to sync
      const details = await getExaminationDetails(editData.application_number);
      setParsedData(details);
      setEditData(details);
    } catch (error) {
      toast.error("Failed to update details.");
    }
  };

  return (
    <div className="w-full pt-8">
      <div className="w-full mb-6">
        <h1 className="text-3xl font-bold text-center mb-1">
          Upload FER Document
        </h1>
        <p className="text-gray-500 text-center mb-4">
          Upload your FER PDF to begin parsing and extracting information.
        </p>
      </div>
      <div
        className="w-full bg-white rounded-xl shadow-lg p-0 overflow-hidden flex flex-col"
        style={{ minHeight: 650 }}
      >
        {/* Top bar with file input and process button */}
        <div className="flex flex-col md:flex-row items-center justify-between px-8 py-4 border-b gap-4">
          <div className="flex items-center gap-4 w-full md:w-auto">
            <input
              ref={fileInputRef}
              type="file"
              accept="application/pdf"
              onChange={handleFileChange}
              className="hidden"
            />
            <button
              className="bg-indigo-600 text-white font-semibold px-4 py-2 rounded shadow hover:bg-indigo-700"
              onClick={() => fileInputRef.current.click()}
            >
              {ferFile ? "Change FER PDF" : "Choose FER PDF"}
            </button>
            {ferFile && (
              <span className="text-gray-700 text-sm truncate max-w-xs">
                {ferFile.name}
              </span>
            )}
          </div>
          <button
            className="bg-green-600 text-white font-semibold px-6 py-2 rounded shadow hover:bg-green-700 disabled:opacity-50 w-full md:w-auto"
            onClick={handleProcess}
            disabled={!ferFile || isProcessing}
          >
            {isProcessing ? "Processing..." : "Process"}
          </button>
        </div>
        {/* Split view: left = PDF preview, right = parsed data */}
        <div
          className="flex flex-col lg:flex-row flex-1 min-h-[500px]"
          style={{ height: 500 }}
        >
          <div className="flex-1 bg-gray-100 flex items-center justify-center border-b lg:border-b-0 lg:border-r min-h-[400px]">
            {pdfUrl ? (
              <iframe
                src={pdfUrl}
                title="FER PDF Preview"
                className="w-full h-full min-h-[400px]"
                style={{ border: "none" }}
              />
            ) : (
              <span className="text-gray-400 text-lg">
                PDF preview will appear here
              </span>
            )}
          </div>
          <div className="flex-1 bg-white flex items-center justify-center min-h-[400px]">
            {editData ? (
              <div className="w-full max-w-2xl p-6">
                <h2 className="text-xl font-bold mb-4 text-center">
                  Parsed Examination Details
                </h2>
                <table className="w-full text-left border mb-4">
                  <tbody>
                    {Object.entries(editData).map(([key, value]) =>
                      key !== "application_number" ? (
                        <tr key={key} className="border-b">
                          <td className="font-semibold py-1 pr-4 capitalize w-1/3">
                            {key.replace(/_/g, " ")}
                          </td>
                          <td className="py-1 w-2/3">
                            <input
                              className="w-full border rounded px-2 py-1 text-gray-700"
                              value={value || ""}
                              onChange={(e) =>
                                handleEditChange(key, e.target.value)
                              }
                            />
                          </td>
                        </tr>
                      ) : null
                    )}
                  </tbody>
                </table>
                <button
                  className="bg-blue-600 text-white font-semibold px-6 py-2 rounded shadow hover:bg-blue-700 w-full"
                  onClick={handleSave}
                >
                  Save Changes
                </button>
              </div>
            ) : (
              <span className="text-gray-400 text-lg">
                Parsed data will appear here after processing
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
