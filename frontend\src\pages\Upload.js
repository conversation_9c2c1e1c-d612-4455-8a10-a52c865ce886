import React, { useRef, useState, useEffect } from "react";
import toast from "react-hot-toast";
import {
  getExaminationDetails,
  uploadDocuments,
  updateExaminationDetails,
  getFerDocumentPath,
  getFerSummary,
} from "../services/api";

export default function Upload({ selectedApplication }) {
  const [ferFile, setFerFile] = useState(null);
  const [pdfUrl, setPdfUrl] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [parsedData, setParsedData] = useState(null);
  const [editData, setEditData] = useState(null);
  const [ferSummaryData, setFerSummaryData] = useState(null);
  const fileInputRef = useRef();

  // Load PDF and parsed data when a chat is selected from the sidebar
  useEffect(() => {
    const fetchData = async () => {
      if (selectedApplication) {
        try {
          const details = await getExaminationDetails(selectedApplication);
          setParsedData(details);
          setEditData(details);

          // Fetch FER summary data
          try {
            const summaryResponse = await getFerSummary(selectedApplication);
            setFerSummaryData(summaryResponse.summary);
          } catch (error) {
            console.log("No FER summary data available yet");
            setFerSummaryData(null);
          }
          // Fetch the FER PDF file path from backend
          const docRes = await getFerDocumentPath(selectedApplication);
          if (docRes.file_path) {
            // Handle both old format (./uploads/...) and new format (/uploads/...)
            let filePath = docRes.file_path;
            if (filePath.startsWith('./')) {
              filePath = filePath.replace(/^\.\//, "/");
            }
            if (!filePath.startsWith('/')) {
              filePath = '/' + filePath;
            }
            // Use absolute URL to backend server
            const backendBaseUrl = "http://192.168.1.191:8000";
            setPdfUrl(`${backendBaseUrl}${filePath}`);
          } else {
            setPdfUrl(null);
          }
          setFerFile(null);
        } catch (error) {
          toast.error("Failed to load examination details.");
        }
      } else {
        setParsedData(null);
        setEditData(null);
        setPdfUrl(null);
        setFerFile(null);
        setFerSummaryData(null);
      }
    };
    fetchData();
  }, [selectedApplication]);

  // Handle file selection
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.type === "application/pdf") {
      setFerFile(file);
      setPdfUrl(URL.createObjectURL(file));
      setParsedData(null);
    } else {
      toast.error("Please select a valid PDF file.");
      setFerFile(null);
      setPdfUrl(null);
    }
  };

  // Handle process button click
  const handleProcess = async () => {
    if (!ferFile) {
      toast.error("Please select a FER PDF to process.");
      return;
    }
    setIsProcessing(true);
    setParsedData(null);
    try {
      const formData = new FormData();
      formData.append("fer", ferFile);
      // Optionally add spec/claims if needed
      const uploadRes = await uploadDocuments(formData);
      // Fetch parsed details using the application_number returned from backend
      const details = await getExaminationDetails(uploadRes.application_number);
      setParsedData(details);
      setEditData(details);
      setIsProcessing(false);
      toast.success("FER processed and details parsed!");
    } catch (error) {
      toast.error("Failed to process FER PDF.");
      setIsProcessing(false);
    }
  };

  // Handle input change for editable fields
  const handleEditChange = (key, value) => {
    setEditData((prev) => ({ ...prev, [key]: value }));
  };

  // Fields that should not be editable or displayed
  const excludedFields = [
    "application_number",
    "created_at",
    "updated_at",
    "createdAt",
    "updatedAt"
  ];

  // Normalize data before sending to backend
  const normalizeEditData = (data) => {
    const normalized = {};
    Object.entries(data).forEach(([key, value]) => {
      if (excludedFields.includes(key)) return; // Don't send excluded fields
      normalized[key] =
        value === ""
          ? null
          : value !== null && value !== undefined
          ? value.toString()
          : null;
    });
    return normalized;
  };

  // Handle save changes
  const handleSave = async () => {
    if (!editData || !editData.application_number) return;
    try {
      const normalized = normalizeEditData(editData);
      await updateExaminationDetails(editData.application_number, normalized);
      toast.success("Examination details updated successfully!");
      // Optionally refetch to sync
      const details = await getExaminationDetails(editData.application_number);
      setParsedData(details);
      setEditData(details);
    } catch (error) {
      toast.error("Failed to update details.");
    }
  };

  return (
    <div className="w-full pt-4 px-2">
      <div className="w-full mb-4">
        <h1 className="text-3xl font-bold text-center mb-1">
          Upload FER Document
        </h1>
        <p className="text-gray-500 text-center mb-4">
          Upload your FER PDF to begin parsing and extracting information.
        </p>
      </div>
      <div
        className="w-full max-w-7xl mx-auto bg-white rounded-xl shadow-lg p-0 overflow-hidden flex flex-col"
        style={{ minHeight: 700 }}
      >
        {/* Top bar with file input and process button */}
        <div className="flex flex-col md:flex-row items-center justify-between px-8 py-4 border-b gap-4">
          <div className="flex items-center gap-4 w-full md:w-auto">
            <input
              ref={fileInputRef}
              type="file"
              accept="application/pdf"
              onChange={handleFileChange}
              className="hidden"
            />
            <button
              className="bg-indigo-600 text-white font-semibold px-4 py-2 rounded shadow hover:bg-indigo-700"
              onClick={() => fileInputRef.current.click()}
            >
              {ferFile ? "Change FER PDF" : "Choose FER PDF"}
            </button>
            {ferFile && (
              <span className="text-gray-700 text-sm truncate max-w-xs">
                {ferFile.name}
              </span>
            )}
          </div>
          <button
            className="bg-green-600 text-white font-semibold px-6 py-2 rounded shadow hover:bg-green-700 disabled:opacity-50 w-full md:w-auto"
            onClick={handleProcess}
            disabled={!ferFile || isProcessing}
          >
            {isProcessing ? "Processing..." : "Process"}
          </button>
        </div>
        {/* Split view: left = PDF preview, right = parsed data */}
        <div
          className="flex flex-col xl:flex-row flex-1 min-h-[600px]"
          style={{ height: 600 }}
        >
          <div className="flex-1 bg-gray-100 flex items-center justify-center border-b xl:border-b-0 xl:border-r min-h-[500px]">
            {pdfUrl ? (
              <iframe
                src={pdfUrl}
                title="FER PDF Preview"
                className="w-full h-full min-h-[500px]"
                style={{ border: "none" }}
              />
            ) : (
              <span className="text-gray-400 text-lg">
                PDF preview will appear here
              </span>
            )}
          </div>
          <div className="flex-1 bg-white flex items-start justify-center min-h-[500px] overflow-y-auto">
            {editData ? (
              <div className="w-full p-4">
                <h2 className="text-xl font-bold mb-4 text-center sticky top-0 bg-white py-2">
                  Parsed Examination Details
                </h2>
                {/* Display Application Number as non-editable */}
                {editData.application_number && (
                  <div className="mb-4 p-3 bg-gray-50 rounded border">
                    <div className="flex justify-between items-center">
                      <span className="font-semibold text-sm text-gray-700">Application Number:</span>
                      <span className="text-sm font-mono bg-white px-2 py-1 rounded border">
                        {editData.application_number}
                      </span>
                    </div>
                  </div>
                )}

                <div className="max-h-[400px] overflow-y-auto">
                  <table className="w-full text-left border mb-4 text-sm">
                    <tbody>
                      {Object.entries(editData)
                        .filter(([key]) => !excludedFields.includes(key))
                        .map(([key, value]) => (
                          <tr key={key} className="border-b hover:bg-gray-50">
                            <td className="font-semibold py-2 pr-3 capitalize w-2/5 text-xs">
                              {key.replace(/_/g, " ")}
                            </td>
                            <td className="py-2 w-3/5">
                              <input
                                className="w-full border rounded px-2 py-1 text-gray-700 text-xs focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                value={value || ""}
                                onChange={(e) =>
                                  handleEditChange(key, e.target.value)
                                }
                                placeholder={value === null ? "No data (--)" : ""}
                              />
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>

                {/* FER Summary Section */}
                {ferSummaryData && (
                  <div className="mt-6 border-t pt-4">
                    <h3 className="text-lg font-bold mb-3 text-center">FER Summary (Part-I)</h3>
                    <div className="max-h-[300px] overflow-y-auto">
                      <table className="w-full text-left border text-sm">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-3 py-2 text-xs font-medium text-gray-500 uppercase border-b">
                              Requirements under the Act
                            </th>
                            <th className="px-3 py-2 text-xs font-medium text-gray-500 uppercase border-b">
                              Claim Numbers
                            </th>
                            <th className="px-3 py-2 text-xs font-medium text-gray-500 uppercase border-b">
                              Remarks
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {[
                            {
                              label: "Novelty",
                              rows: [
                                { claims: ferSummaryData.novelty_claims1, remark: ferSummaryData.novelty_remark1 },
                                { claims: ferSummaryData.novelty_claims2, remark: ferSummaryData.novelty_remark2 }
                              ]
                            },
                            {
                              label: "Inventive Step",
                              rows: [
                                { claims: ferSummaryData.inventive_step_claims1, remark: ferSummaryData.inventive_step_remark1 },
                                { claims: ferSummaryData.inventive_step_claims2, remark: ferSummaryData.inventive_step_remark2 }
                              ]
                            },
                            {
                              label: "Industrial Applicability",
                              rows: [
                                { claims: ferSummaryData.industrial_applicability_claims1, remark: ferSummaryData.industrial_applicability_remark1 },
                                { claims: ferSummaryData.industrial_applicability_claims2, remark: ferSummaryData.industrial_applicability_remark2 }
                              ]
                            },
                            {
                              label: "Nonpatentability u/s 3",
                              rows: [
                                { claims: ferSummaryData.section_3_claims1, remark: ferSummaryData.section_3_remark1 },
                                { claims: ferSummaryData.section_3_claims2, remark: ferSummaryData.section_3_remark2 }
                              ]
                            },
                            {
                              label: "Non-patentability u/s 4",
                              rows: [
                                { claims: ferSummaryData.section_4_claims1, remark: ferSummaryData.section_4_remark1 },
                                { claims: ferSummaryData.section_4_claims2, remark: ferSummaryData.section_4_remark2 }
                              ]
                            },
                            {
                              label: "Unity of invention u/s 10(5)",
                              rows: [
                                { claims: ferSummaryData.unity_of_invention_claims1, remark: ferSummaryData.unity_of_invention_remark1 },
                                { claims: ferSummaryData.unity_of_invention_claims2, remark: ferSummaryData.unity_of_invention_remark2 }
                              ]
                            },
                            {
                              label: "Sufficiency of disclosure u/s 10(4)",
                              rows: [
                                { claims: null, remark: ferSummaryData.sufficiency_of_disclosure_remark1 }
                              ]
                            },
                            {
                              label: "Reference to co-pending/foreign applications",
                              rows: [
                                { claims: null, remark: ferSummaryData.reference_to_foreign_applications_remark1 }
                              ]
                            },
                            {
                              label: "Clarity/Conciseness",
                              rows: [
                                { claims: ferSummaryData.clarity_claims1, remark: ferSummaryData.clarity_remark1 },
                                { claims: ferSummaryData.clarity_claims2, remark: ferSummaryData.clarity_remark2 }
                              ]
                            },
                            {
                              label: "Definitive",
                              rows: [
                                { claims: ferSummaryData.definitive_claims1, remark: ferSummaryData.definitive_remark1 },
                                { claims: ferSummaryData.definitive_claims2, remark: ferSummaryData.definitive_remark2 }
                              ]
                            },
                            {
                              label: "Supported by description",
                              rows: [
                                { claims: ferSummaryData.supported_by_description_claims1, remark: ferSummaryData.supported_by_description_remark1 },
                                { claims: ferSummaryData.supported_by_description_claims2, remark: ferSummaryData.supported_by_description_remark2 }
                              ]
                            },
                            {
                              label: "Scope",
                              rows: [
                                { claims: ferSummaryData.scope_claims1, remark: ferSummaryData.scope_remark1 },
                                { claims: ferSummaryData.scope_claims2, remark: ferSummaryData.scope_remark2 }
                              ]
                            }
                          ].map((item, index) => {
                            // Filter out empty rows
                            const validRows = item.rows.filter(row => row.claims || row.remark);
                            if (validRows.length === 0) return null;

                            return validRows.map((row, rowIndex) => (
                              <tr key={`${index}-${rowIndex}`} className="border-b hover:bg-gray-50">
                                {rowIndex === 0 && (
                                  <td
                                    className="px-3 py-2 text-xs font-medium text-gray-900 border-r"
                                    rowSpan={validRows.length}
                                  >
                                    {item.label}
                                  </td>
                                )}
                                <td className="px-3 py-2 text-xs text-gray-700">
                                  {row.claims || "--"}
                                </td>
                                <td className="px-3 py-2">
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    row.remark?.toLowerCase().includes('yes') ? 'bg-green-100 text-green-800' :
                                    row.remark?.toLowerCase().includes('no') ? 'bg-red-100 text-red-800' :
                                    'bg-gray-100 text-gray-800'
                                  }`}>
                                    {row.remark || "--"}
                                  </span>
                                </td>
                              </tr>
                            ));
                          }).filter(Boolean)}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                <div className="sticky bottom-0 bg-white pt-4">
                  <button
                    className="bg-blue-600 text-white font-semibold px-6 py-2 rounded shadow hover:bg-blue-700 w-full"
                    onClick={handleSave}
                  >
                    Save Changes
                  </button>
                </div>
              </div>
            ) : (
              <span className="text-gray-400 text-lg">
                Parsed data will appear here after processing
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
