import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import { uploadDocuments } from "../services/api";
import LoadingSpinner from "./LoadingSpinner";

const FileInput = ({ label, isRequired, onChange, file }) => {
  const fileName = file ? file.name : "No file chosen";
  return (
    <div className="col-span-1">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {isRequired && <span className="text-red-500">*</span>}
      </label>
      <div className="mt-1 flex items-center">
        <label className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
          <span>Choose File</span>
          <input
            type="file"
            className="hidden"
            accept=".pdf"
            onChange={onChange}
          />
        </label>
        <span className="ml-3 text-sm text-gray-500 truncate">{fileName}</span>
      </div>
    </div>
  );
};

export default function FileUpload() {
  const [appNumber, setAppNumber] = useState("");
  const [ferFile, setFerFile] = useState(null);
  const [specFile, setSpecFile] = useState(null);
  const [claimsFile, setClaimsFile] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!appNumber || !ferFile) {
      toast.error("Application Number and FER Document are required.");
      return;
    }
    setIsLoading(true);
    const formData = new FormData();
    formData.append("application_number", appNumber);
    formData.append("fer", ferFile);
    if (specFile) formData.append("specification", specFile);
    if (claimsFile) formData.append("claims", claimsFile);

    try {
      toast.loading("Uploading and processing...");
      const response = await uploadDocuments(formData);
      toast.dismiss();
      toast.success(response.message || "Files uploaded successfully!");
      navigate(`/summary/${response.patent_id}`);
    } catch (error) {
      toast.dismiss();
      const errorMsg =
        error.response?.data?.detail || "Upload failed. Please try again.";
      toast.error(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-200 w-full">
      {isLoading ? (
        <div className="py-12">
          <LoadingSpinner message="Uploading files and generating summary..." />
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label
              htmlFor="appNumber"
              className="block text-sm font-medium text-gray-700"
            >
              Application Number <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="appNumber"
              value={appNumber}
              onChange={(e) => setAppNumber(e.target.value)}
              className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <FileInput
              label="FER Document"
              isRequired={true}
              file={ferFile}
              onChange={(e) => setFerFile(e.target.files[0])}
            />
            <FileInput
              label="Specification Document"
              file={specFile}
              onChange={(e) => setSpecFile(e.target.files[0])}
            />
            <FileInput
              label="Claims Document"
              file={claimsFile}
              onChange={(e) => setClaimsFile(e.target.files[0])}
            />
          </div>
          <div className="text-right pt-4">
            <button
              type="submit"
              className="w-full md:w-auto bg-indigo-600 text-white font-bold py-3 px-8 rounded-lg shadow-md hover:bg-indigo-700 transition-colors disabled:opacity-50"
              disabled={isLoading}
            >
              Upload & Start
            </button>
          </div>
        </form>
      )}
    </div>
  );
}
