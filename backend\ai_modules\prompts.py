# Prompts for AI text generation

FER_SUMMARY_PROMPT = """
You are a legal AI assistant. Below is a First Examination Report (FER) from the Indian Patent Office.
Generate a professional, neutral summary highlighting the examiner's key objections, references cited, and section-wise issues.

FER Document Content:
{fer_text}

Patent Specification (if available):
{spec_text}

Claims (if available):
{claims_text}

Output in clear, structured format with numbered points.
"""

OBJECTION_RESPONSE_PROMPT = """
You are a legal AI assistant. Below is a First Examination Report (FER) from the Indian Patent Office.
Identify each objection raised by the examiner and generate a professional, section-wise response for each objection.

FER Document Content:
{fer_text}

Patent Specification (if available):
{spec_text}

Claims (if available):
{claims_text}

Output in the following format:
1. Objection: <text>
   Response: <AI-generated response>
2. Objection: <text>
   Response: <AI-generated response>
... and so on for each objection.
"""

def build_summary_prompt(fer_text, spec_text=None, claims_text=None):
    return FER_SUMMARY_PROMPT.format(
        fer_text=fer_text or '',
        spec_text=spec_text or '',
        claims_text=claims_text or ''
    )

def build_objection_prompt(fer_text, spec_text=None, claims_text=None):
    return OBJECTION_RESPONSE_PROMPT.format(
        fer_text=fer_text or '',
        spec_text=spec_text or '',
        claims_text=claims_text or ''
    ) 