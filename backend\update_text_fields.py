#!/usr/bin/env python3
"""
Quick migration script to update VARCHAR(100) fields to TEXT for long remark fields
"""

import mysql.connector
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'fer_reply')
}

def update_text_fields():
    """
    Update VARCHAR(100) fields to TEXT for long remark fields
    """
    try:
        # Connect to database
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("🔄 Updating VARCHAR fields to TEXT for long remarks...")
        
        # Update the fields that can contain long text
        updates = [
            "ALTER TABLE application_fer_summary MODIFY COLUMN sufficiency_of_disclosure_remark1 TEXT",
            "ALTER TABLE application_fer_summary MODIFY COLUMN reference_to_foreign_applications_remark1 TEXT"
        ]
        
        for update_sql in updates:
            print(f"📝 Executing: {update_sql}")
            cursor.execute(update_sql)
        
        connection.commit()
        print("✅ Successfully updated VARCHAR fields to TEXT")
        
    except mysql.connector.Error as err:
        print(f"❌ Error: {err}")
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
    
    return True

if __name__ == "__main__":
    success = update_text_fields()
    if success:
        print("🎉 Migration completed successfully!")
    else:
        print("💥 Migration failed!")
