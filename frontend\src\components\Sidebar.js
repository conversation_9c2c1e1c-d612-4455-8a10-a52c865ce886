import React, { useState, useEffect } from "react";
import { Link, NavLink, useNavigate, useLocation } from "react-router-dom";
import {
  listApplications,
  deleteApplication,
  listExaminationDetails,
} from "../services/api";
import toast from "react-hot-toast";

export default function Sidebar({ onSelectApplication }) {
  const [applications, setApplications] = useState([]);
  const [examinations, setExaminations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    fetchApplications();
    fetchExaminations();
    // Re-fetch when location changes (e.g., after delete)
    // eslint-disable-next-line
  }, [location.pathname]);

  const fetchApplications = async () => {
    setIsLoading(true);
    try {
      const response = await listApplications();
      setApplications(response.applications || []);
    } catch (error) {
      toast.error("Failed to fetch application list.");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchExaminations = async () => {
    setIsLoading(true);
    try {
      const response = await listExaminationDetails();
      setExaminations(response.examinations || []);
    } catch (error) {
      toast.error("Failed to fetch examination list.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = (applicationNumber) => {
    toast(
      (t) => (
        <span className="flex flex-col items-start">
          <span className="font-semibold text-red-600 mb-1">Delete application?</span>
          <span className="text-xs text-gray-700 mb-2">
            This will permanently delete application and all related data for{" "}
            <b>{applicationNumber}</b>.
          </span>
          <div className="flex gap-2 mt-1">
            <button
              className="bg-red-600 text-white px-3 py-1 rounded text-xs font-semibold hover:bg-red-700"
              onClick={async () => {
                toast.dismiss(t.id);
                try {
                  await deleteApplication(applicationNumber);
                  toast.success("Application deleted successfully");
                  setApplications((prev) => prev.filter((app) => app.application_number !== applicationNumber));
                  if (location.pathname.includes(applicationNumber)) {
                    navigate("/");
                  }
                } catch {
                  toast.error("Failed to delete application");
                }
              }}
            >
              Delete
            </button>
            <button
              className="bg-gray-200 text-gray-700 px-3 py-1 rounded text-xs font-semibold hover:bg-gray-300"
              onClick={() => toast.dismiss(t.id)}
            >
              Cancel
            </button>
          </div>
        </span>
      ),
      { duration: 8000 }
    );
  };

  const linkClasses =
    "block w-full text-left px-3 py-2 text-sm text-gray-600 rounded-md hover:bg-gray-100 transition-colors truncate";
  const activeLinkClasses = "bg-indigo-50 text-indigo-600 font-semibold";

  return (
    <aside className="w-64 bg-white border-r border-gray-200 p-4 flex flex-col space-y-4">
      <button
        onClick={() => onSelectApplication(null)}
        className="w-full text-center bg-indigo-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:bg-indigo-700 transition-colors"
      >
        + New Reply
      </button>
      <div className="flex-1 overflow-y-auto space-y-1 pr-2">
        <h2 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
          Recent Chats
        </h2>
        {isLoading ? (
          <p className="text-sm text-gray-500">Loading...</p>
        ) : examinations.length > 0 ? (
          examinations.map((exam) => (
            <button
              key={exam.application_number}
              className={`${linkClasses}`}
              onClick={() => onSelectApplication(exam.application_number)}
            >
              {exam.application_number}
            </button>
          ))
        ) : applications.length > 0 ? (
          applications.map((application) => (
            <div key={application.application_number} className="flex items-center group">
              <NavLink
                to={`/summary/${application.application_number}`}
                className={({ isActive }) =>
                  `${linkClasses} ${isActive ? activeLinkClasses : ""} flex-1`
                }
              >
                {application.application_number}
              </NavLink>
              <button
                title="Delete application"
                onClick={() =>
                  handleDelete(application.application_number)
                }
                className="ml-2 p-2 rounded hover:bg-red-100 text-gray-400 hover:text-red-600 transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2"
                  />
                </svg>
              </button>
            </div>
          ))
        ) : (
          <p className="text-sm text-gray-500">No applications yet.</p>
        )}
      </div>
    </aside>
  );
}
