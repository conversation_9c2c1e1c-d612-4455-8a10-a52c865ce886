import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import toast from "react-hot-toast";
import ModelSelector from "../components/ModelSelector";
import LoadingSpinner from "../components/LoadingSpinner";
import {
  generateObjectionResponses,
  getApplicationDetails,
  saveObjections,
} from "../services/api";
import SectionNavigator from "../components/SectionNavigator";
import PdfViewer from "../components/PdfViewer";
import FerSummary from "../components/FerSummary";

export default function Reply() {
  const { application_number } = useParams();
  const [application, setApplication] = useState(null);
  const [responses, setResponses] = useState("");
  const [editedResponses, setEditedResponses] = useState("");
  const [selectedModel, setSelectedModel] = useState("gemini");
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    const fetchApplicationDetails = async () => {
      try {
        const response = await getApplicationDetails(application_number);
        setApplication(response);
        setResponses(response.objection_responses || "");
        setEditedResponses(response.objection_responses || "");
      } catch (error) {
        toast.error("Failed to fetch application details.");
      }
    };
    fetchApplicationDetails();
  }, [application_number]);

  const handleGenerateResponses = async () => {
    setIsLoading(true);
    try {
      const response = await generateObjectionResponses(
        application_number,
        selectedModel
      );
      setResponses(response.responses);
      setEditedResponses(response.responses);
      toast.success("Objection responses generated successfully!");
    } catch (error) {
      toast.error("Failed to generate objection responses.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await saveObjections(application_number, editedResponses);
      setResponses(editedResponses);
      toast.success("Objection responses saved successfully!");
    } catch (error) {
      toast.error("Failed to save objection responses.");
    } finally {
      setIsSaving(false);
    }
  };

  const isResponsesChanged = responses !== editedResponses;

  return (
    <div>
      <h1 className="text-3xl font-bold text-gray-900 mb-2">
        {application ? application.application_number : "Loading..."}
      </h1>
      <p className="text-md text-gray-500 mb-8">
        Manage the summary and objection responses for your patent application.
      </p>

      <SectionNavigator application_number={application_number} />

      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
          <ModelSelector
            selectedModel={selectedModel}
            onModelChange={setSelectedModel}
          />
          <button
            onClick={handleGenerateResponses}
            disabled={isLoading}
            className="mt-4 md:mt-0 w-full md:w-auto bg-indigo-600 text-white font-bold py-2 px-6 rounded-lg shadow-md hover:bg-indigo-700 transition-colors disabled:opacity-50"
          >
            {isLoading ? "Generating..." : "Generate Objection Responses"}
          </button>
        </div>
      </div>

      {/* FER Summary Section */}
      <div className="mt-6">
        <FerSummary applicationNumber={application_number} />
      </div>

      {/* Split layout: PDF viewer and responses */}
      <div className="mt-6 grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* PDF Viewer */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-800">FER Document</h2>
          </div>
          <PdfViewer
            applicationNumber={application_number}
            className="h-96 rounded-b-xl"
          />
        </div>

        {/* Responses Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-800">
              Generated Objection Responses
            </h2>
            {(responses || editedResponses) && (
              <button
                onClick={handleSave}
                disabled={isSaving || !isResponsesChanged}
                className="bg-green-600 text-white font-bold py-2 px-4 rounded-lg shadow-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving ? "Saving..." : "Save Changes"}
              </button>
            )}
          </div>
          {isLoading ? (
            <div className="h-96 flex items-center justify-center">
              <LoadingSpinner message="Generating AI Responses..." />
            </div>
          ) : (
            <textarea
              value={editedResponses}
              onChange={(e) => setEditedResponses(e.target.value)}
              className="w-full h-96 p-4 border-none focus:ring-0 text-gray-700 bg-transparent resize-none"
              placeholder="Your generated objection responses will appear here. You can edit them directly."
            />
          )}
        </div>
      </div>
    </div>
  );
}
