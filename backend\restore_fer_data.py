#!/usr/bin/env python3
"""
Restore FER summary data from backup
"""

import mysql.connector
from models.db import get_db
import json

def restore_data():
    """Restore FER summary data from backup"""
    try:
        with open('fer_summary_backup.json', 'r') as f:
            backup_data = json.load(f)
    except FileNotFoundError:
        print("❌ No backup file found")
        return

    conn = get_db()
    try:
        with conn.cursor() as cursor:
            for record in backup_data:
                cursor.execute("""
                    INSERT INTO application_fer_summary (
                        application_number,
                        novelty_claims1, novelty_remark1, novelty_claims2, novelty_remark2,
                        inventive_step_claims1, inventive_step_remark1, inventive_step_claims2, inventive_step_remark2,
                        industrial_applicability_claims1, industrial_applicability_remark1, industrial_applicability_claims2, industrial_applicability_remark2,
                        section_3_claims1, section_3_remark1, section_3_claims2, section_3_remark2,
                        section_4_claims1, section_4_remark1, section_4_claims2, section_4_remark2,
                        unity_of_invention_claims1, unity_of_invention_remark1, unity_of_invention_claims2, unity_of_invention_remark2,
                        sufficiency_of_disclosure_remark1,
                        reference_to_foreign_applications_remark1,
                        clarity_claims1, clarity_remark1, clarity_claims2, clarity_remark2,
                        definitive_claims1, definitive_remark1, definitive_claims2, definitive_remark2,
                        supported_by_description_claims1, supported_by_description_remark1, supported_by_description_claims2, supported_by_description_remark2,
                        scope_claims1, scope_remark1, scope_claims2, scope_remark2
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, (
                    record['application_number'],
                    record.get('novelty_claims1'), record.get('novelty_remark1'), record.get('novelty_claims2'), record.get('novelty_remark2'),
                    record.get('inventive_step_claims1'), record.get('inventive_step_remark1'), record.get('inventive_step_claims2'), record.get('inventive_step_remark2'),
                    record.get('industrial_applicability_claims1'), record.get('industrial_applicability_remark1'), record.get('industrial_applicability_claims2'), record.get('industrial_applicability_remark2'),
                    record.get('section_3_claims1'), record.get('section_3_remark1'), record.get('section_3_claims2'), record.get('section_3_remark2'),
                    record.get('section_4_claims1'), record.get('section_4_remark1'), record.get('section_4_claims2'), record.get('section_4_remark2'),
                    record.get('unity_of_invention_claims1'), record.get('unity_of_invention_remark1'), record.get('unity_of_invention_claims2'), record.get('unity_of_invention_remark2'),
                    record.get('sufficiency_of_disclosure_remark1'),
                    record.get('reference_to_foreign_applications_remark1'),
                    record.get('clarity_claims1'), record.get('clarity_remark1'), record.get('clarity_claims2'), record.get('clarity_remark2'),
                    record.get('definitive_claims1'), record.get('definitive_remark1'), record.get('definitive_claims2'), record.get('definitive_remark2'),
                    record.get('supported_by_description_claims1'), record.get('supported_by_description_remark1'), record.get('supported_by_description_claims2'), record.get('supported_by_description_remark2'),
                    record.get('scope_claims1'), record.get('scope_remark1'), record.get('scope_claims2'), record.get('scope_remark2')
                ))
            
            conn.commit()
            print(f"✅ Restored {len(backup_data)} records")
    finally:
        conn.close()

if __name__ == "__main__":
    restore_data()
