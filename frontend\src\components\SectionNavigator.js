import React from "react";
import { NavLink, useLocation } from "react-router-dom";

const SectionNavigator = ({ patent_id }) => {
  const { pathname } = useLocation();

  const steps = [
    { name: "Summary", path: `/summary/${patent_id}` },
    { name: "Objection Reply", path: `/reply/${patent_id}` },
  ];

  const activeIndex = steps.findIndex((step) =>
    pathname.startsWith(step.path.substring(0, step.path.lastIndexOf("/")))
  );

  return (
    <div className="w-full mb-8">
      <div className="flex items-center">
        {steps.map((step, index) => {
          const isCompleted = activeIndex > index;
          const isActive = activeIndex === index;

          let circleClasses =
            "w-10 h-10 rounded-full flex items-center justify-center font-bold border-2 transition-all duration-300";
          let textClasses =
            "absolute top-12 text-sm text-center transition-all duration-300 w-32";

          if (isActive) {
            circleClasses += " bg-white border-red-500 text-red-500 scale-110";
            textClasses += " text-red-500 font-semibold";
          } else if (isCompleted) {
            circleClasses += " bg-red-500 border-red-500 text-white";
            textClasses += " text-gray-700";
          } else {
            circleClasses += " bg-white border-gray-300 text-gray-400";
            textClasses += " text-gray-400";
          }

          return (
            <React.Fragment key={step.name}>
              <NavLink
                to={step.path}
                className="flex flex-col items-center relative flex-shrink-0"
              >
                <div className={circleClasses}>{index + 1}</div>
                <p className={textClasses}>{step.name}</p>
              </NavLink>
              {index < steps.length - 1 && (
                <div
                  className={`flex-1 h-0.5 mx-2 ${
                    isCompleted ? "bg-red-500" : "bg-gray-300"
                  }`}
                />
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default SectionNavigator;
