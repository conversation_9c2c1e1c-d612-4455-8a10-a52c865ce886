import os
import requests
from enum import Enum
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class ModelType(str, Enum):
    OLLAMA = "ollama"
    GEMINI = "gemini"

# --- Model Configuration ---
OLLAMA_URLS = [
    "http://localhost:11434/api/generate",
    "http://127.0.0.1:11434/api/generate",
    "http://*************:11434/api/generate"
]
OLLAMA_MODEL = "llama3.2:latest"

GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")

# --- Initialize Clients ---
if GEMINI_API_KEY:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
        print("✅ Gemini API configured successfully")
    except Exception as e:
        print(f"⚠️ Error configuring Gemini API: {e}")
else:
    print("⚠️ Warning: GEMINI_API_KEY not found in environment. Gemini will not be available.")

def _get_ollama_response(prompt: str) -> str:
    """Tries to get a response from a list of local Ollama URLs."""
    payload = {"model": OLLAMA_MODEL, "prompt": prompt, "stream": False}
    for url in OLLAMA_URLS:
        try:
            response = requests.post(url, json=payload, timeout=60)
            response.raise_for_status()
            return response.json().get("response", "")
        except (requests.exceptions.RequestException, ConnectionError) as e:
            print(f"Could not connect to Ollama at {url}. Trying next...")
            continue
    raise ConnectionError("Could not connect to any of the specified Ollama instances.")

def _get_gemini_response(prompt: str) -> str:
    """Gets a response from the Gemini API."""
    if not GEMINI_API_KEY:
        raise ValueError("Cannot generate with Gemini: API key not configured.")
    model = genai.GenerativeModel('gemini-2.0-flash')
    response = model.generate_content(prompt)
    return response.text

def generate_text(model_choice: str, prompt: str) -> str:
    """
    Generates text using the chosen AI model.
    """
    if model_choice == ModelType.OLLAMA:
        return _get_ollama_response(prompt)
    elif model_choice == ModelType.GEMINI:
        return _get_gemini_response(prompt)
    else:
        raise ValueError(f"Invalid model choice: {model_choice}. Must be 'ollama' or 'gemini'.") 