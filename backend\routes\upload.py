from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import Optional
import os
from models.db import get_db
from utils.pdf_parser import extract_text_pdfminer as parse_pdf
from utils.pdf_parser import parse_application_examination_details

router = APIRouter()
UPLOAD_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'uploads')
os.makedirs(UPLOAD_DIR, exist_ok=True)

def save_file(upload_file: UploadFile, patent_id: int, file_type: str, application_number: str):
    uploads_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads', application_number)
    os.makedirs(uploads_dir, exist_ok=True)
    filename = upload_file.filename
    file_path = os.path.join(uploads_dir, filename)
    with open(file_path, "wb") as buffer:
        buffer.write(upload_file.file.read())
    return file_path

@router.post('/upload')
def upload_documents(
    fer: UploadFile = File(...),
    specification: Optional[UploadFile] = File(None),
    claims: Optional[UploadFile] = File(None)
):
    conn = get_db()
    try:
        # 1. Parse the FER PDF first to get the application_number
        fer_path_tmp = os.path.join(UPLOAD_DIR, fer.filename)
        with open(fer_path_tmp, "wb") as buffer:
            buffer.write(fer.file.read())
        fer_text = parse_pdf(fer_path_tmp)
        details = parse_application_examination_details(fer_text)
        print("[DEBUG] Parsed application examination details:", details)
        application_number = details.get('application_number')
        if not application_number:
            raise HTTPException(status_code=400, detail="Could not extract application number from FER PDF.")

        with conn.cursor() as cursor:
            # 2. Create a new patent record
            cursor.execute("INSERT INTO patents (application_number) VALUES (%s)", (application_number,))
            cursor.execute("""
                INSERT INTO application_examination_details (
                    application_number, date_of_dispatch, to_address, to_email, date_of_filing, date_of_priority,
                    pct_application_num_and_date, applicant_name, request_for_examination_no_and_date, date_of_publication,
                    name_of_examinar, examinar_location, name_of_controller, controller_email, controller_location, due_date_of_response
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    date_of_dispatch=VALUES(date_of_dispatch),
                    to_address=VALUES(to_address),
                    to_email=VALUES(to_email),
                    date_of_filing=VALUES(date_of_filing),
                    date_of_priority=VALUES(date_of_priority),
                    pct_application_num_and_date=VALUES(pct_application_num_and_date),
                    applicant_name=VALUES(applicant_name),
                    request_for_examination_no_and_date=VALUES(request_for_examination_no_and_date),
                    date_of_publication=VALUES(date_of_publication),
                    name_of_examinar=VALUES(name_of_examinar),
                    examinar_location=VALUES(examinar_location),
                    name_of_controller=VALUES(name_of_controller),
                    controller_email=VALUES(controller_email),
                    controller_location=VALUES(controller_location),
                    due_date_of_response=VALUES(due_date_of_response)
            """,
            (
                details.get('application_number'),
                details.get('date_of_dispatch'),
                details.get('to_address'),
                details.get('to_email'),
                details.get('date_of_filing'),
                details.get('date_of_priority'),
                details.get('pct_application_num_and_date'),
                details.get('applicant_name'),
                details.get('request_for_examination_no_and_date'),
                details.get('date_of_publication'),
                details.get('name_of_examinar'),
                details.get('examinar_location'),
                details.get('name_of_controller'),
                details.get('controller_email'),
                details.get('controller_location'),
                details.get('due_date_of_response')
            ))

            # 3. Move the FER file to the correct folder
            uploads_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads', application_number)
            os.makedirs(uploads_dir, exist_ok=True)
            fer_path = os.path.join(uploads_dir, fer.filename)
            os.replace(fer_path_tmp, fer_path)
            cursor.execute("INSERT INTO documents (application_number, type, file_path, extracted_text) VALUES (%s, %s, %s, %s)",
                           (application_number, 'FER', fer_path, fer_text))

            # 4. Save application examination details
            cursor.execute("""
                INSERT INTO application_examination_details (
                    application_number, date_of_dispatch, to_address, to_email, date_of_filing, date_of_priority,
                    pct_application_num_and_date, applicant_name, request_for_examination_no_and_date, date_of_publication,
                    name_of_examinar, examinar_location, name_of_controller, controller_email, controller_location, due_date_of_response
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    date_of_dispatch=VALUES(date_of_dispatch),
                    to_address=VALUES(to_address),
                    to_email=VALUES(to_email),
                    date_of_filing=VALUES(date_of_filing),
                    date_of_priority=VALUES(date_of_priority),
                    pct_application_num_and_date=VALUES(pct_application_num_and_date),
                    applicant_name=VALUES(applicant_name),
                    request_for_examination_no_and_date=VALUES(request_for_examination_no_and_date),
                    date_of_publication=VALUES(date_of_publication),
                    name_of_examinar=VALUES(name_of_examinar),
                    examinar_location=VALUES(examinar_location),
                    name_of_controller=VALUES(name_of_controller),
                    controller_email=VALUES(controller_email),
                    controller_location=VALUES(controller_location),
                    due_date_of_response=VALUES(due_date_of_response)
            """,
            (
                details.get('application_number'),
                details.get('date_of_dispatch'),
                details.get('to_address'),
                details.get('to_email'),
                details.get('date_of_filing'),
                details.get('date_of_priority'),
                details.get('pct_application_num_and_date'),
                details.get('applicant_name'),
                details.get('request_for_examination_no_and_date'),
                details.get('date_of_publication'),
                details.get('name_of_examinar'),
                details.get('examinar_location'),
                details.get('name_of_controller'),
                details.get('controller_email'),
                details.get('controller_location'),
                details.get('due_date_of_response')
            ))

            # 5. Save and process optional files
            if specification:
                spec_path = os.path.join(uploads_dir, specification.filename)
                with open(spec_path, "wb") as buffer:
                    buffer.write(specification.file.read())
                spec_text = parse_pdf(spec_path)
                cursor.execute("INSERT INTO documents (application_number, type, file_path, extracted_text) VALUES (%s, %s, %s, %s)",
                               (application_number, 'Specification', spec_path, spec_text))

            if claims:
                claims_path = os.path.join(uploads_dir, claims.filename)
                with open(claims_path, "wb") as buffer:
                    buffer.write(claims.file.read())
                claims_text = parse_pdf(claims_path)
                cursor.execute("INSERT INTO documents (application_number, type, file_path, extracted_text) VALUES (%s, %s, %s, %s)",
                               (application_number, 'Claims', claims_path, claims_text))
            conn.commit()
            return {"patent_id": cursor.lastrowid, "application_number": application_number, "parsed_details": details}
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close() 