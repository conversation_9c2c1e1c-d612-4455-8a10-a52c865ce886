-- Table 1: application_examination_details
CREATE TABLE IF NOT EXISTS application_examination_details (
    application_number VARCHAR(50) PRIMARY KEY,
    date_of_dispatch DATE,
    to_address TEXT,
    to_email VARCHAR(255),
    date_of_filing DATE,
    date_of_priority DATE,
    pct_application_num_and_date VARCHAR(255),
    applicant_name VARCHAR(255),
    request_for_examination_no_and_date VARCHAR(255),
    date_of_publication DATE,
    name_of_examinar VARCHAR(255),
    examinar_location VARCHAR(255),
    name_of_controller VARCHAR(255),
    controller_email VARCHAR(255),
    controller_location VARCHAR(255),
    due_date_of_response DATE,
    summary LONGTEXT,
    objection_responses LONGTEXT,
    summary_generated_by ENUM('ollama', 'gemini', 'combined'),
    objections_generated_by <PERSON><PERSON><PERSON>('ollama', 'gemini', 'combined'),
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table 2: documents
CREATE TABLE IF NOT EXISTS documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_number VARCHAR(50) NOT NULL,
    type ENUM('FER', 'Specification', 'Claims'),
    file_path TEXT,
    extracted_text LONGTEXT,
    uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_number) REFERENCES application_examination_details(application_number) ON DELETE CASCADE
);

-- Table 3: application_fer_summary
CREATE TABLE IF NOT EXISTS application_fer_summary (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_number VARCHAR(50) NOT NULL,

    -- Novelty
    novelty_claims1 VARCHAR(100),
    novelty_remark1 VARCHAR(100),
    novelty_claims2 VARCHAR(100),
    novelty_remark2 VARCHAR(100),

    -- Inventive Step
    inventive_step_claims1 VARCHAR(100),
    inventive_step_remark1 VARCHAR(100),
    inventive_step_claims2 VARCHAR(100),
    inventive_step_remark2 VARCHAR(100),

    -- Industrial Applicability
    industrial_applicability_claims1 VARCHAR(100),
    industrial_applicability_remark1 VARCHAR(100),
    industrial_applicability_claims2 VARCHAR(100),
    industrial_applicability_remark2 VARCHAR(100),

    -- Section 3
    section_3_claims1 VARCHAR(100),
    section_3_remark1 VARCHAR(100),
    section_3_claims2 VARCHAR(100),
    section_3_remark2 VARCHAR(100),

    -- Section 4
    section_4_claims1 VARCHAR(100),
    section_4_remark1 VARCHAR(100),
    section_4_claims2 VARCHAR(100),
    section_4_remark2 VARCHAR(100),

    -- Unity of Invention
    unity_of_invention_claims1 VARCHAR(100),
    unity_of_invention_remark1 VARCHAR(100),
    unity_of_invention_claims2 VARCHAR(100),
    unity_of_invention_remark2 VARCHAR(100),

    -- Sufficiency of Disclosure
    sufficiency_of_disclosure_remark1 TEXT,

    -- Reference to foreign applications
    reference_to_foreign_applications_remark1 TEXT,

    -- Clarity
    clarity_claims1 VARCHAR(100),
    clarity_remark1 VARCHAR(100),
    clarity_claims2 VARCHAR(100),
    clarity_remark2 VARCHAR(100),

    -- Definitive
    definitive_claims1 VARCHAR(100),
    definitive_remark1 VARCHAR(100),
    definitive_claims2 VARCHAR(100),
    definitive_remark2 VARCHAR(100),

    -- Supported by Description
    supported_by_description_claims1 VARCHAR(100),
    supported_by_description_remark1 VARCHAR(100),
    supported_by_description_claims2 VARCHAR(100),
    supported_by_description_remark2 VARCHAR(100),

    -- Scope
    scope_claims1 VARCHAR(100),
    scope_remark1 VARCHAR(100),
    scope_claims2 VARCHAR(100),
    scope_remark2 VARCHAR(100),

    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (application_number)
        REFERENCES application_examination_details(application_number)
        ON DELETE CASCADE
);