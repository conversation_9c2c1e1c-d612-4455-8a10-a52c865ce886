-- Table 1: application_examination_details
CREATE TABLE IF NOT EXISTS application_examination_details (
    application_number VARCHAR(50) PRIMARY KEY,
    date_of_dispatch DATE,
    to_address TEXT,
    to_email VARCHAR(255),
    date_of_filing DATE,
    date_of_priority DATE,
    pct_application_num_and_date VARCHAR(255),
    applicant_name VARCHAR(255),
    request_for_examination_no_and_date VARCHAR(255),
    date_of_publication DATE,
    name_of_examinar VARCHAR(255),
    examinar_location VARCHAR(255),
    name_of_controller VARCHAR(255),
    controller_email VARCHAR(255),
    controller_location VARCHAR(255),
    due_date_of_response DATE,
    summary LONGTEXT,
    objection_responses LONGTEXT,
    summary_generated_by ENUM('ollama', 'gemini', 'combined'),
    objections_generated_by <PERSON><PERSON><PERSON>('ollama', 'gemini', 'combined'),
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table 2: documents
CREATE TABLE IF NOT EXISTS documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_number VARCHAR(50) NOT NULL,
    type ENUM('FER', 'Specification', 'Claims'),
    file_path TEXT,
    extracted_text LONGTEXT,
    uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_number) REFERENCES application_examination_details(application_number) ON DELETE CASCADE
);

-- Table 3: application_fer_summary
CREATE TABLE IF NOT EXISTS application_fer_summary (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_number VARCHAR(50) NOT NULL,

    -- Section 2(1)(j) - Invention
    novelty_claims VARCHAR(100),
    novelty_remark ENUM('Yes', 'No'),

    inventive_step_claims VARCHAR(100),
    inventive_step_remark ENUM('Yes', 'No'),

    industrial_applicability_claims VARCHAR(100),
    industrial_applicability_remark ENUM('Yes', 'No'),

    -- Section 3 - Non-patentability
    section_3_claims VARCHAR(100),
    section_3_remark ENUM('Yes', 'No'),

    -- Section 4 - Non-patentability u/s 4
    section_4_claims VARCHAR(100),
    section_4_remark ENUM('Yes', 'No'),

    -- Section 10(5) - Unity of invention
    unity_of_invention_claims VARCHAR(100),
    unity_of_invention_remark ENUM('Yes', 'No'),

    -- Section 10(4) - Sufficiency of disclosure
    sufficiency_of_disclosure ENUM('Yes', 'No'),

    -- Co-pending/foreign applications
    reference_to_foreign_applications ENUM('Yes', 'No'),

    -- Section 10(5) & 10(4)(c) - Claim Clarity
    clarity_claims VARCHAR(100),
    clarity_remark ENUM('Yes', 'No'),

    definitive_claims VARCHAR(100),
    definitive_remark ENUM('Yes', 'No'),

    supported_by_description_claims VARCHAR(100),
    supported_by_description_remark ENUM('Yes', 'No'),

    scope_claims VARCHAR(100),
    scope_remark ENUM('Yes', 'No'),

    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (application_number)
        REFERENCES application_examination_details(application_number)
        ON DELETE CASCADE
);