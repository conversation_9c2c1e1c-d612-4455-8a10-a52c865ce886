-- Table 1: application_examination_details
CREATE TABLE IF NOT EXISTS application_examination_details (
    application_number VARCHAR(32) PRIMARY KEY,
    date_of_dispatch DATE,
    to_address TEXT,
    to_email VARCHAR(255),
    date_of_filing DATE,
    date_of_priority DATE,
    pct_application_num_and_date VARCHAR(255),
    applicant_name VARCHAR(255),
    request_for_examination_no_and_date VARCHAR(255),
    date_of_publication DATE,
    name_of_examinar VARCHAR(255),
    examinar_location VARCHAR(255),
    name_of_controller VARCHAR(255),
    controller_email VARCHAR(255),
    controller_location VARCHAR(255),
    due_date_of_response DATE,
    summary LONGTEXT,
    objection_responses LONGTEXT,
    summary_generated_by ENUM('ollama', 'gemini', 'combined'),
    objections_generated_by <PERSON><PERSON><PERSON>('ollama', 'gemini', 'combined'),
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table 2: documents
CREATE TABLE IF NOT EXISTS documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_number VARCHAR(32) NOT NULL,
    type ENUM('FER', 'Specification', 'Claims'),
    file_path TEXT,
    extracted_text LONGTEXT,
    uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_number) REFERENCES application_examination_details(application_number) ON DELETE CASCADE
);