from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os
from . import upload, patents

app = FastAPI()

origins = [
    "http://localhost",
    "http://localhost:3000",
    "http://*************",
    "http://*************:3000",
    "http://*************:8000",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(upload.router, prefix="/api")
app.include_router(patents.router, prefix="/api")

# Mount static files for PDF serving
uploads_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')
app.mount("/uploads", StaticFiles(directory=uploads_dir), name="uploads")

@app.get("/health")
def health_check():
    return {"status": "ok"} 