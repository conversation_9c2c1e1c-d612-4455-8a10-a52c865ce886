from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from . import upload, summary, objection, patents

app = FastAPI()

origins = [
    "http://localhost",
    "http://localhost:3000",
    "http://*************",
    "http://*************:3000",
    "http://*************:8000",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(upload.router, prefix="/api")
app.include_router(summary.router, prefix="/api")
app.include_router(objection.router, prefix="/api")
app.include_router(patents.router, prefix="/api")

@app.get("/health")
def health_check():
    return {"status": "ok"} 