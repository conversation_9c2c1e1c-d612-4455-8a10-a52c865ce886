import React, { useState } from "react";
import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import { Toaster } from "react-hot-toast";
import Home from "./pages/Home";
import Upload from "./pages/Upload";
import Navbar from "./components/Navbar";
import Sidebar from "./components/Sidebar";

function App() {
  const [isSidebarOpen, setSidebarOpen] = useState(true);
  const [selectedApplication, setSelectedApplication] = useState(null);

  const toggleSidebar = () => {
    setSidebarOpen(!isSidebarOpen);
  };

  return (
    <Router>
      <div className="bg-gray-100 min-h-screen">
        <Toaster position="top-right" />
        <Navbar onToggleSidebar={toggleSidebar} />
        <div className="flex" style={{ height: "calc(100vh - 4rem)" }}>
          {isSidebarOpen && (
            <Sidebar onSelectApplication={setSelectedApplication} />
          )}
          <main className="flex-1 overflow-y-auto">
            <div className="max-w-full mx-auto p-2 md:p-4">
              <Routes>
                <Route
                  path="/"
                  element={<Upload selectedApplication={selectedApplication} />}
                />
                <Route
                  path="/upload"
                  element={<Upload selectedApplication={selectedApplication} />}
                />
              </Routes>
            </div>
          </main>
        </div>
      </div>
    </Router>
  );
}

export default App;
