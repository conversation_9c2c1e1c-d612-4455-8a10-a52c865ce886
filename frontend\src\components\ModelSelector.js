import React from "react";

const ModelSelector = ({ selectedModel, onModelChange }) => {
  // Common classes for both buttons for a consistent look
  const commonButtonClasses =
    "px-4 py-2 text-sm font-semibold rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center space-x-2";

  const getButtonClasses = (model) => {
    // If this model is the selected one, give it the "active" style
    if (selectedModel === model) {
      return "bg-indigo-600 text-white shadow";
    }
    // Otherwise, give it the default style
    return "bg-white text-gray-700 border border-gray-300 hover:bg-gray-50";
  };

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Select AI Model
      </label>
      <div className="flex items-center space-x-2 p-1 bg-gray-100 rounded-lg w-min">
        <button
          onClick={() => onModelChange("gemini")}
          className={`${commonButtonClasses} ${getButtonClasses("gemini")}`}
        >
          <span>✨</span>
          <span>Gemini</span>
        </button>
        <button
          onClick={() => onModelChange("ollama")}
          className={`${commonButtonClasses} ${getButtonClasses("ollama")}`}
        >
          <span>🦙</span>
          <span>Ollama</span>
        </button>
      </div>
    </div>
  );
};

export default ModelSelector;
