import mysql.connector
from mysql.connector import pooling
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# --- Database Connection ---
# Securely fetch credentials from environment variables.
# The values are loaded from the .env file in your backend directory.
db_config = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD'),
    'database': os.getenv('DB_NAME', 'fer_reply')
}

# --- FOR DEBUGGING ONLY ---
# Print the credentials the app is trying to use.
# Check your console output when the server starts.
print("--- DATABASE CONNECTION DEBUG ---")
print(f"Attempting to connect with:")
print(f"  Host: {db_config['host']}")
print(f"  User: {db_config['user']}")
# Check if password is None or empty and print an appropriate message
print(f"  Password Loaded: {'Yes' if db_config['password'] else 'No'}")
print(f"  Database: {db_config['database']}")
print("---------------------------------")
# --------------------------

try:
    db_pool = mysql.connector.pooling.MySQLConnectionPool(
        pool_name="fer_pool",
        pool_size=5,
        **db_config
    )
    print("✅ Database connection pool created successfully.")
except mysql.connector.Error as err:
    print(f"❌ Error creating database connection pool: {err}")
    db_pool = None

def get_db():
    if db_pool is None:
        raise Exception("Database connection pool is not available.")
    return db_pool.get_connection() 