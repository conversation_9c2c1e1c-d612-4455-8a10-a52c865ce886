#!/usr/bin/env python3
"""
Migration script to update application_fer_summary table structure
to support multiple rows per requirement (claims1/remark1, claims2/remark2)
"""

import mysql.connector
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'fer_reply')
}

def migrate_fer_summary_table():
    """
    Migrate the application_fer_summary table to new structure
    """
    try:
        # Connect to database
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("🔄 Starting migration of application_fer_summary table...")
        
        # Step 1: Backup existing data
        print("📦 Backing up existing data...")
        cursor.execute("SELECT * FROM application_fer_summary")
        existing_data = cursor.fetchall()
        
        # Get column names for backup
        cursor.execute("DESCRIBE application_fer_summary")
        columns = [row[0] for row in cursor.fetchall()]
        
        print(f"📊 Found {len(existing_data)} existing records")
        
        # Step 2: Drop the existing table
        print("🗑️ Dropping existing table...")
        cursor.execute("DROP TABLE IF EXISTS application_fer_summary")
        
        # Step 3: Create new table with updated structure
        print("🏗️ Creating new table structure...")
        create_table_sql = """
        CREATE TABLE application_fer_summary (
            id INT AUTO_INCREMENT PRIMARY KEY,
            application_number VARCHAR(50) NOT NULL,

            -- Novelty
            novelty_claims1 VARCHAR(100),
            novelty_remark1 VARCHAR(100),
            novelty_claims2 VARCHAR(100),
            novelty_remark2 VARCHAR(100),

            -- Inventive Step
            inventive_step_claims1 VARCHAR(100),
            inventive_step_remark1 VARCHAR(100),
            inventive_step_claims2 VARCHAR(100),
            inventive_step_remark2 VARCHAR(100),

            -- Industrial Applicability
            industrial_applicability_claims1 VARCHAR(100),
            industrial_applicability_remark1 VARCHAR(100),
            industrial_applicability_claims2 VARCHAR(100),
            industrial_applicability_remark2 VARCHAR(100),

            -- Section 3
            section_3_claims1 VARCHAR(100),
            section_3_remark1 VARCHAR(100),
            section_3_claims2 VARCHAR(100),
            section_3_remark2 VARCHAR(100),

            -- Section 4
            section_4_claims1 VARCHAR(100),
            section_4_remark1 VARCHAR(100),
            section_4_claims2 VARCHAR(100),
            section_4_remark2 VARCHAR(100),

            -- Unity of Invention
            unity_of_invention_claims1 VARCHAR(100),
            unity_of_invention_remark1 VARCHAR(100),
            unity_of_invention_claims2 VARCHAR(100),
            unity_of_invention_remark2 VARCHAR(100),

            -- Sufficiency of Disclosure
            sufficiency_of_disclosure_remark1 VARCHAR(100),

            -- Reference to foreign applications
            reference_to_foreign_applications_remark1 VARCHAR(100),

            -- Clarity
            clarity_claims1 VARCHAR(100),
            clarity_remark1 VARCHAR(100),
            clarity_claims2 VARCHAR(100),
            clarity_remark2 VARCHAR(100),

            -- Definitive
            definitive_claims1 VARCHAR(100),
            definitive_remark1 VARCHAR(100),
            definitive_claims2 VARCHAR(100),
            definitive_remark2 VARCHAR(100),

            -- Supported by Description
            supported_by_description_claims1 VARCHAR(100),
            supported_by_description_remark1 VARCHAR(100),
            supported_by_description_claims2 VARCHAR(100),
            supported_by_description_remark2 VARCHAR(100),

            -- Scope
            scope_claims1 VARCHAR(100),
            scope_remark1 VARCHAR(100),
            scope_claims2 VARCHAR(100),
            scope_remark2 VARCHAR(100),

            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (application_number)
                REFERENCES application_examination_details(application_number)
                ON DELETE CASCADE
        )
        """
        
        cursor.execute(create_table_sql)
        
        # Step 4: Migrate existing data to new structure
        if existing_data:
            print("🔄 Migrating existing data to new structure...")
            
            # Create mapping from old fields to new fields (first row only)
            field_mapping = {
                'novelty_claims': 'novelty_claims1',
                'novelty_remark': 'novelty_remark1',
                'inventive_step_claims': 'inventive_step_claims1',
                'inventive_step_remark': 'inventive_step_remark1',
                'industrial_applicability_claims': 'industrial_applicability_claims1',
                'industrial_applicability_remark': 'industrial_applicability_remark1',
                'section_3_claims': 'section_3_claims1',
                'section_3_remark': 'section_3_remark1',
                'section_4_claims': 'section_4_claims1',
                'section_4_remark': 'section_4_remark1',
                'unity_of_invention_claims': 'unity_of_invention_claims1',
                'unity_of_invention_remark': 'unity_of_invention_remark1',
                'sufficiency_of_disclosure': 'sufficiency_of_disclosure_remark1',
                'reference_to_foreign_applications': 'reference_to_foreign_applications_remark1',
                'clarity_claims': 'clarity_claims1',
                'clarity_remark': 'clarity_remark1',
                'definitive_claims': 'definitive_claims1',
                'definitive_remark': 'definitive_remark1',
                'supported_by_description_claims': 'supported_by_description_claims1',
                'supported_by_description_remark': 'supported_by_description_remark1',
                'scope_claims': 'scope_claims1',
                'scope_remark': 'scope_remark1'
            }
            
            for row in existing_data:
                # Create a dictionary from the row data
                row_dict = dict(zip(columns, row))
                
                # Build insert statement for new structure
                new_values = {}
                new_values['application_number'] = row_dict.get('application_number')
                
                # Map old fields to new fields
                for old_field, new_field in field_mapping.items():
                    if old_field in row_dict:
                        new_values[new_field] = row_dict[old_field]
                
                # Insert into new table
                placeholders = ', '.join(['%s'] * len(new_values))
                columns_str = ', '.join(new_values.keys())
                values_list = list(new_values.values())
                
                insert_sql = f"INSERT INTO application_fer_summary ({columns_str}) VALUES ({placeholders})"
                cursor.execute(insert_sql, values_list)
            
            print(f"✅ Successfully migrated {len(existing_data)} records")
        
        # Commit the changes
        connection.commit()
        print("✅ Migration completed successfully!")
        
        # Verify the new structure
        cursor.execute("DESCRIBE application_fer_summary")
        new_columns = cursor.fetchall()
        print(f"📋 New table has {len(new_columns)} columns")
        
        cursor.execute("SELECT COUNT(*) FROM application_fer_summary")
        record_count = cursor.fetchone()[0]
        print(f"📊 Table contains {record_count} records")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        if 'connection' in locals():
            connection.rollback()
        raise
    
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    migrate_fer_summary_table()
