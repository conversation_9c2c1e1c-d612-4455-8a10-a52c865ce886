import React, { useState, useEffect } from "react";
import { getFerDocumentPath } from "../services/api";
import toast from "react-hot-toast";

export default function PdfViewer({ applicationNumber, className = "" }) {
  const [pdfUrl, setPdfUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPdf = async () => {
      if (!applicationNumber) {
        setPdfUrl(null);
        return;
      }

      setIsLoading(true);
      setError(null);
      
      try {
        const docRes = await getFerDocumentPath(applicationNumber);
        if (docRes.file_path) {
          // Construct full URL for PDF
          const baseUrl = "http://192.168.1.191:8000"; // Same as API base without /api
          const fullUrl = `${baseUrl}${docRes.file_path}`;
          setPdfUrl(fullUrl);
        } else {
          setError("PDF file not found");
        }
      } catch (error) {
        console.error("Failed to load PDF:", error);
        setError("Failed to load PDF document");
        toast.error("Failed to load PDF document");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPdf();
  }, [applicationNumber]);

  if (isLoading) {
    return (
      <div className={`bg-gray-100 flex items-center justify-center ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-2"></div>
          <span className="text-gray-500">Loading PDF...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-gray-100 flex items-center justify-center ${className}`}>
        <div className="text-center text-gray-500">
          <svg className="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (!pdfUrl) {
    return (
      <div className={`bg-gray-100 flex items-center justify-center ${className}`}>
        <div className="text-center text-gray-500">
          <svg className="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p>No PDF document available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gray-100 ${className}`}>
      <iframe
        src={pdfUrl}
        title="FER PDF Document"
        className="w-full h-full"
        style={{ border: "none", minHeight: "500px" }}
      />
    </div>
  );
}
