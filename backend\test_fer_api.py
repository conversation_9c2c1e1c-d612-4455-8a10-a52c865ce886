#!/usr/bin/env python3
"""
Test FER Summary API endpoints
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_get_fer_summary():
    """Test getting FER summary"""
    print("Testing GET /api/fer-summary/202221057268")
    response = requests.get(f"{BASE_URL}/fer-summary/202221057268")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print("✅ GET request successful")
        print(f"Summary data keys: {list(data['summary'].keys()) if data['summary'] else 'None'}")
        return data['summary']
    else:
        print(f"❌ GET request failed: {response.text}")
        return None

def test_update_fer_summary(original_data):
    """Test updating FER summary"""
    if not original_data:
        print("❌ No original data to update")
        return
    
    print("\nTesting PUT /api/fer-summary/202221057268")
    
    # Modify some data
    update_data = {
        "novelty_claims1": "1-5",  # Changed from "1-9"
        "novelty_remark1": "Yes",  # Changed from "No"
        "inventive_step_claims1": "6-9",
        "inventive_step_remark1": "Yes"
    }
    
    response = requests.put(f"{BASE_URL}/fer-summary/202221057268", json=update_data)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        print("✅ PUT request successful")
        print(f"Response: {response.json()}")
    else:
        print(f"❌ PUT request failed: {response.text}")

def main():
    print("🧪 Testing FER Summary API endpoints\n")
    
    # Test GET
    original_data = test_get_fer_summary()
    
    # Test UPDATE
    test_update_fer_summary(original_data)
    
    # Test GET again to verify update
    print("\n🔄 Verifying update...")
    updated_data = test_get_fer_summary()
    
    if updated_data:
        print(f"\nNovelty claims1: {updated_data.get('novelty_claims1')}")
        print(f"Novelty remark1: {updated_data.get('novelty_remark1')}")

if __name__ == "__main__":
    main()
